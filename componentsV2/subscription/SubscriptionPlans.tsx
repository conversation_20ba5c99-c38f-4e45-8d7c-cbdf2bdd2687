import React from "react";
import PlanCard from "@/componentsV2/subscription/PlanCard";
import { Plan, UserSubscription } from "@/componentsV2/subscription/data";
import { SUBSCRIPTION_TIERS } from "@/lib/constants";
import { Plan as DBPlan, Subscription } from "@/lib/db/schema";

type SubscriptionTier = (typeof SUBSCRIPTION_TIERS)[number];
interface SubscriptionPlansProps {
	plans: Plan[];
	userSubscription: UserSubscription;
	billingCycle: "monthly" | "annual";
	processingTier: string | null;
	tabAnimating: boolean;
	onSubscribe: (
		tier: "free" | "premium" | "premium-yearly" | "enterprise",
	) => void;
	userSubscriptionTier: SubscriptionTier;
	activePlans: DBPlan[];
	activeSubscription?: Subscription;
	hideButtons?: boolean;
}

const SubscriptionPlans = ({
	plans,
	userSubscription,
	billingCycle,
	processingTier,
	tabAnimating,
	onSubscribe,
	userSubscriptionTier,
	activePlans,
	activeSubscription,
	hideButtons,
}: SubscriptionPlansProps) => {
	// Separate regular plans from enterprise plan

	const regularPlans = plans.filter(
		(plan) =>
			plan.id !== "enterprise" &&
			!(userSubscriptionTier !== "free" && plan.id === "free"),
	);
	const enterprisePlan = plans.find((plan) => plan.id === "enterprise");
	return (
		<div
			className={`space-y-10 max-w-5xl mx-auto mb-16 ${
				tabAnimating
					? "opacity-70 transition-opacity"
					: "opacity-100 transition-opacity"
			}`}
		>
			{/* Regular plans in a 2-column grid if plans are more than 2*/}

			<div
				className={`grid gap-6  ${
					regularPlans.length === 1 ? "justify-center" : "md:grid-cols-2"
				}`}
			>
				{regularPlans.map((plan) => {
					const isUserPlan = userSubscriptionTier === plan.id;
					const currentPlanPrice =
						activePlans?.find(
							(activePlanObj) => activePlanObj?.name === plan?.id,
						)?.price ?? "0";
					const planDetails = { ...plan, price: currentPlanPrice };
					return (
						<PlanCard
							key={plan.id}
							{...planDetails}
							isUserPlan={isUserPlan}
							isProcessing={processingTier === plan.id}
							billingCycle={billingCycle}
							onSubscribe={onSubscribe}
							activeSubscription={activeSubscription}
							hideButtons={hideButtons}
						/>
					);
				})}
			</div>

			{/* Enterprise plan in full width */}
			{enterprisePlan && (
				<div className="mt-10" id="enterprise-section">
					<PlanCard
						{...enterprisePlan}
						isUserPlan={userSubscription.tier === enterprisePlan.id}
						isProcessing={processingTier === enterprisePlan.id}
						billingCycle={billingCycle}
						onSubscribe={onSubscribe}
					/>
				</div>
			)}
		</div>
	);
};

export default SubscriptionPlans;
