import React from "react";
import WelcomeHeader from "@/componentsV2/welcome/WelcomeHeader";

const SubscriptionHeader = ({ page }: any) => {
	return (
		<div className="flex flex-col items-center mb-6 ">
			{/* {page !== "welcome" && (
        <Button variant="ghost" size="sm" className="self-start mb-6" asChild>
          <Link
            href="/"
            className="flex flex-row gap-3 items-center transition-colors hover:bg-primary hover:text-primary-foreground"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Link>
        </Button>
      )} */}

			<div className="text-center max-w-2xl">
				{page === "welcome" ? (
					<WelcomeHeader />
				) : (
					<h1 className="text-4xl font-bold tracking-tight mb-4 font-playfair text-[rgb(var(--title-color))] from-[#1A1F2C] via-[#394151] to-[#6D7A8C] bg-clip-text dark:text-[rgb(var(--base-navy))]">
						Unlock Iqidis Core
					</h1>
				)}
			</div>
		</div>
	);
};

export default SubscriptionHeader;
