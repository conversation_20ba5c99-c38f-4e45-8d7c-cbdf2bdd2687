"use client";

import type { ChatRequestOptions } from "ai";
import type { ExtendedMessage } from "@/lib/types";
import cx from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { memo, useMemo, useState, useRef, useEffect } from "react";
import { marked } from "marked";
import { useCopyToClipboard } from "usehooks-ts";
import React from "react";
import { formatDistanceToNow, format } from "date-fns";

import type { Vote } from "@/lib/db/schema";

import { DocumentToolCall, DocumentToolResult } from "@/componentsV2/document";
import {
	PencilEditIcon,
	SparklesIcon,
	SparklingSparkleIcon,
	CopyIcon,
	LogoIqidis,
} from "@/componentsV2/icons";
import { Markdown } from "@/componentsV2/markdown";
import { MessageActions } from "@/componentsV2/message-actions";
import { PreviewAttachment } from "@/componentsV2/preview-attachment";
import { Weather } from "@/componentsV2/weather";
import equal from "fast-deep-equal";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { MessageEditor } from "@/componentsV2/message-editor";
import { DocumentPreview } from "@/componentsV2/document-preview";
import { toast } from "sonner";
import { RewrittenQueryDisplay } from "@/componentsV2/rewritten-query-display";
import { Logger } from "@/lib/utils/Logger";
import { ErrorMessage } from "@/componentsV2/error-message";
import { fixMessageContent } from "@/lib/formatting/utils";
import { CitationVerificationDisplay } from "@/componentsV2/citation-verification-display";
import { logEvent } from "@/lib/analytics/events-client";
import { ChatEvent } from "@/lib/analytics/event-types";

const PurePreviewMessage = ({
	chatId,
	message,
	vote,
	isLoading,
	setMessages,
	reload,
	isReadonly,
	onRetry,
	onEdit,
	isMessageLoading,
	isLastMessage,
}: {
	chatId: string;
	message: ExtendedMessage;
	vote: Vote | undefined;
	isLoading: boolean;
	setMessages: (
		messages:
			| ExtendedMessage[]
			| ((messages: ExtendedMessage[]) => ExtendedMessage[]),
	) => void;
	reload: (
		chatRequestOptions?: ChatRequestOptions,
	) => Promise<string | null | undefined>;
	isReadonly: boolean;
	onRetry?: (userMessageId?: string) => void;
	onEdit?: (newContent: string) => void;
	isMessageLoading: boolean;
	isLastMessage?: boolean;
}) => {
	const [mode, setMode] = useState<"view" | "edit">("view");
	const [showCopyTooltip, setShowCopyTooltip] = useState(false);
	const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
	const markdownRef = useRef<HTMLDivElement>(null);
	const [_, copyToClipboard] = useCopyToClipboard();

	// Check if this is an error message
	const isErrorMessage = Boolean(message.metadata?.error);

	const formatMessageTime = (timestamp: string | Date) => {
		if (!timestamp) return "";
		const date =
			typeof timestamp === "string" ? new Date(timestamp) : timestamp;

		// Always show full date and time format
		return format(date, "MMM d, yyyy, h:mm a");
	};

	useEffect(() => {
		const handleSelection = () => {
			// Showing 'copy' tooltip if user selects text
			const selection = window.getSelection();
			if (selection && selection.toString().length > 0) {
				// Check if selection is within our markdown component
				let isWithinMarkdown = false;
				if (markdownRef.current) {
					let node = selection.anchorNode;
					while (node) {
						if (node === markdownRef.current) {
							isWithinMarkdown = true;
							break;
						}
						node = node.parentNode;
					}
				}

				if (isWithinMarkdown) {
					const range = selection.getRangeAt(0);
					const rect = range.getBoundingClientRect();
					setTooltipPosition({
						x: rect.left + rect.width / 2,
						y: rect.top - 10,
					});
					setShowCopyTooltip(true);
				}
			} else {
				setShowCopyTooltip(false);
			}
		};

		document.addEventListener("selectionchange", handleSelection);
		return () =>
			document.removeEventListener("selectionchange", handleSelection);
	}, []);

	const handleCopySelection = async () => {
		const selection = window.getSelection();

		if (selection && !selection.isCollapsed) {
			try {
				// Get the selected text
				const selectedText = selection.toString();

				// Get the HTML representation of the selection
				const range = selection.getRangeAt(0);
				const fragment = range.cloneContents();
				const tempDiv = document.createElement("div");
				tempDiv.appendChild(fragment);
				const selectedHtml = tempDiv.innerHTML;

				try {
					// Try to use the Clipboard API with both formats
					await navigator.clipboard.write([
						new ClipboardItem({
							"text/html": new Blob([selectedHtml], { type: "text/html" }),
							"text/plain": new Blob([selectedText], { type: "text/plain" }),
						}),
					]);
				} catch (err) {
					// Fallback to the copyToClipboard function
					await copyToClipboard(selectedText);
				}

				// Show success message
				toast.success("Copied to clipboard!");
				logEvent(ChatEvent.COPY_MESSAGE, {
					messageLength: selectedText.length,
					messageWordCount: selectedText.split(" ").length,
				});

				// Hide the tooltip
				setShowCopyTooltip(false);
			} catch (error) {
				console.error("Error copying selection:", error);
				toast.error("Failed to copy text");
			}
		}
	};

	// Simplified check - just see if previous message is not user
	const isPreviousMessageNonUser = () => {
		// Only run on client side
		if (typeof document === "undefined") return false;

		const messages = document.querySelectorAll("[data-role]");
		const currentIndex = Array.from(messages).findIndex(
			(el) => el.getAttribute("data-message-id") === message.id,
		);
		if (currentIndex <= 0) return false;

		const previousRole = messages[currentIndex - 1].getAttribute("data-role");
		return previousRole !== "user";
	};

	return (
		// <AnimatePresence>
		//   <motion.div
		//     className="w-full mx-auto max-w-3xl px-4 group/message mt-4"
		//     initial={{ y: 5, opacity: 0 }}
		//     animate={{ y: 0, opacity: 1 }}
		//     data-role={message.role}
		//     data-message-id={message.id}
		//     data-has-metadata={Boolean(message.metadata)}
		//     data-has-internet-results={Boolean(message.metadata?.internetResults)}
		//     data-has-error={isErrorMessage}
		//     data-has-relevant-images={Boolean(
		//       message.metadata?.relevantImages?.length
		//     )}
		//   >
		<>
			<div
				className={cn(
					"flex gap-1 sm:gap-2 md:gap-3 lg:gap-5 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-full xl:group-data-[role=user]/message:max-w-2xl",
					{
						"w-full": mode === "edit",
						"group-data-[role=user]/message:w-fit": mode !== "edit",
						"pb-2 sm:pb-4": message.role === "assistant",
					},
				)}
			>
				{message.role === "assistant" && !isPreviousMessageNonUser() && (
					<div className="flex-shrink-0 hidden sm:flex">
						<LogoIqidis size={40} className="sm:w-[50px] sm:h-[50px] flex-none" />
					</div>
				)}
				{message.role === "assistant" && isPreviousMessageNonUser() && (
					<div className="w-6 sm:w-8 flex-shrink-0 hidden sm:block" />
				)}

				<div className={cn("flex flex-col gap-1 sm:gap-2 w-full min-w-0")}>
					{message.experimental_attachments &&
						message.experimental_attachments.length > 0 && (
							<div className="flex flex-row gap-2 overflow-x-auto pb-2 max-w-full scrollbar-thin scrollbar-thumb-zinc-400 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent">
								{message.experimental_attachments.map((attachment) => (
									<PreviewAttachment
										key={attachment.url}
										attachment={attachment}
										disablePreview={attachment.fromPlaybook}
									/>
								))}
							</div>
						)}

					{message.content && mode === "view" && (
						<div className={`flex flex-row gap-1 sm:gap-2 items-start w-full min-w-0 ${message.role === "user" ? "justify-end" : "justify-start"}`}>
							{message.role === "user" && false && !isReadonly && (
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											variant="ghost"
											className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100 flex-shrink-0"
											onClick={() => {
												setMode("edit");
											}}
											disabled={isMessageLoading}
										>
											<PencilEditIcon />
										</Button>
									</TooltipTrigger>
									<TooltipContent>Edit message</TooltipContent>
								</Tooltip>
							)}

							<div
								className={cn("flex flex-col gap-2 sm:gap-4 max-w-full w-fit min-w-0", {
									"user-message-light dark:user-message-dark":
										message.role === "user",
									"assistant-message-light dark:assistant-message-dark":
										message.role === "assistant",
								})}
							>
								<div
									className={cn(
										"text-wrap break-words overflow-wrap-anywhere pl-1 pr-1 pt-2 w-full",
										{
											"markdown-content-main": message.role === "assistant",
											"markdown-content-main-hide":
												message.content.length === 0,
										},
									)}
									ref={markdownRef}
								>
									{message.role === "assistant" &&
									isMessageLoading &&
									isLastMessage ? (
										<motion.div
											initial="hidden"
											animate="show"
											variants={{
												hidden: {
													opacity: 0,
													y: 20,
													transition: {
														duration: 0.5,
														ease: "easeOut",
													},
												},
												show: {
													opacity: 1,
													y: 0,
													transition: {
														staggerChildren: 0.3,
														delayChildren: 0.3,
													},
												},
											}}
										>
											<AnimatePresence>
												{message.content
													.split(/\n\s*\n/)
													.filter(Boolean)
													.map((block: any, i) => (
														<motion.div
															className="markdown-content-main-show"
															key={i}
															variants={{
																hidden: { opacity: 0, y: 20 },
																show: {
																	opacity: 1,
																	y: 0,
																	transition: {
																		duration: 0.5,
																		ease: "easeOut",
																	},
																},
															}}
														>
															<Markdown role={message.role}>
																{fixMessageContent(block)}
															</Markdown>
														</motion.div>
													))}
											</AnimatePresence>
										</motion.div>
									) : (
										<Markdown role={message.role}>
											{fixMessageContent(message.content)}
										</Markdown>
									)}

									{message.createdAt && (
										<>
											<div className="border-t border-border/30 my-1 w-full"></div>
											<div
												className="text-xs opacity-70 group-hover/message:opacity-100 transition-opacity
                          group-data-[role=user]/message:text-primary-foreground/80
                          group-data-[role=assistant]/message:text-muted-foreground
                          dark:group-data-[role=user]/message:text-gray-800"
											>
												{formatMessageTime(message.createdAt)}
											</div>
										</>
									)}
								</div>

								{/* Add error message UI */}
								{isErrorMessage && (
									<ErrorMessage
										message={message}
										onRetry={onRetry}
										chatId={chatId}
									/>
								)}
							</div>
						</div>
					)}

					{message.role === "assistant" &&
						(message.metadata?.rewrittenQuery ||
							message.metadata?.chainOfThoughts) && (
							<React.Suspense fallback={null}>
								<div className="flex items-center flex-wrap gap-y-2">
									{(() => {
										try {
											return <RewrittenQueryDisplay message={message} />;
										} catch (error) {
											Logger.error(
												"Error rendering RewrittenQueryDisplay:",
												error,
											);
											return null;
										}
									})()}

									{/* Add the Citation Verification Display component */}
									{message.role === "assistant" &&
										(() => {
											try {
												return (
													<CitationVerificationDisplay message={message} />
												);
											} catch (error) {
												Logger.error(
													"Error rendering CitationVerificationDisplay:",
													error,
												);
												return null;
											}
										})()}
								</div>
							</React.Suspense>
						)}

					{message.content && mode === "edit" && (
						<div className="flex flex-row gap-2 items-start">
							<div className="size-8" />

							<MessageEditor
								key={message.id}
								message={message}
								setMode={setMode}
								setMessages={setMessages}
								reload={reload}
								onEdit={onEdit}
							/>
						</div>
					)}

					{message.toolInvocations && message.toolInvocations.length > 0 && (
						<div className="flex flex-col gap-4">
							{message.toolInvocations.map((toolInvocation) => {
								const { toolName, toolCallId, state, args } = toolInvocation;

								if (state === "result") {
									const { result } = toolInvocation;

									return (
										<div key={toolCallId}>
											{toolName === "createDocument" ? (
												<DocumentPreview
													isReadonly={isReadonly}
													result={result}
												/>
											) : toolName === "updateDocument" ? (
												<DocumentToolResult
													type="update"
													result={result}
													isReadonly={isReadonly}
												/>
											) : toolName === "requestSuggestions" ? (
												<DocumentToolResult
													type="request-suggestions"
													result={result}
													isReadonly={isReadonly}
												/>
											) : (
												<pre>{JSON.stringify(result, null, 2)}</pre>
											)}
										</div>
									);
								}
								return (
									<div
										key={toolCallId}
										className={cx({
											skeleton: ["getWeather"].includes(toolName),
										})}
									>
										{toolName === "createDocument" ? (
											<DocumentPreview isReadonly={isReadonly} args={args} />
										) : toolName === "updateDocument" ? (
											<DocumentToolCall
												type="update"
												args={args}
												isReadonly={isReadonly}
											/>
										) : toolName === "requestSuggestions" ? (
											<DocumentToolCall
												type="request-suggestions"
												args={args}
												isReadonly={isReadonly}
											/>
										) : null}
									</div>
								);
							})}
						</div>
					)}

					{/* For assistant messages, put timestamp and actions outside the paper container */}
					{/* {message.role === "assistant" && message.createdAt && (
              <div className="text-xs mt-1.5 ml-1 opacity-70 group-hover/message:opacity-100 transition-opacity text-muted-foreground">
                {formatMessageTime(message.createdAt)}
              </div>
            )} */}

					<div
						className={cn(
							isReadonly ? "invisible pointer-events-none" : "visible",
						)}
					>
						<MessageActions
							key={`action-${message.id}`}
							chatId={chatId}
							message={message}
							vote={vote}
							isLoading={isLoading}
							onRetry={onRetry}
							setMode={setMode}
							onEdit={onEdit}
							isMessageLoading={isMessageLoading}
						/>
					</div>
				</div>
			</div>
			{showCopyTooltip && (
				<div
					className="fixed z-50 bg-popover text-popover-foreground px-2 py-1 rounded shadow-md text-xs"
					style={{
						left: `${tooltipPosition.x}px`,
						top: `${tooltipPosition.y}px`,
						transform: "translate(-50%, -100%)",
					}}
				>
					<button
						className="flex items-center gap-1 hover:bg-muted p-1 rounded"
						onClick={handleCopySelection}
					>
						<CopyIcon size={12} />
						Copy
					</button>
				</div>
			)}
		</>
		// </motion.div>
		//</AnimatePresence>
	);
};

export const PreviewMessage = memo(
	PurePreviewMessage,
	(prevProps, nextProps) => {
		if (prevProps.isLoading !== nextProps.isLoading) return false;
		if (prevProps.message.content !== nextProps.message.content) return false;
		if (
			!equal(
				prevProps.message.toolInvocations,
				nextProps.message.toolInvocations,
			)
		)
			return false;
		if (!equal(prevProps.vote, nextProps.vote)) return false;
		// Add metadata comparison
		if (!equal(prevProps.message.metadata, nextProps.message.metadata))
			return false;
		if (prevProps.isMessageLoading !== nextProps.isMessageLoading) return false;

		return true;
	},
);

export const ThinkingMessage = () => {
	const role = "assistant";

	return (
		<motion.div
			className="w-full mx-auto max-w-3xl px-4 group/message "
			initial={{ y: 5, opacity: 0 }}
			animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
			data-role={role}
		>
			<div
				className={cx(
					"flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl",
					{
						"group-data-[role=user]/message:bg-muted": true,
					},
				)}
			>
				<div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border">
					<SparklingSparkleIcon size={14} />
				</div>

				<div className="flex flex-col gap-2 w-full">
					<div className="flex flex-col gap-4 text-muted-foreground">
						Thinking ...
					</div>
				</div>
			</div>
		</motion.div>
	);
};
