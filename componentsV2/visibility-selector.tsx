"use client";

import { ReactNode, useMemo, useState } from "react";
import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

import {
	CheckCircleFillIcon,
	ChevronDownIcon,
	GlobeIcon,
	LockIcon,
} from "@/componentsV2/icons";
import { useChatVisibility } from "@/hooks/use-chat-visibility";

export type VisibilityType = "private" | "public";

const visibilities: Array<{
	id: VisibilityType;
	label: string;
	description: string;
	icon: ReactNode;
}> = [
	{
		id: "private",
		label: "Private",
		description: "Only you can access this chat",
		icon: <LockIcon />,
	},
	{
		id: "public",
		label: "Shared",
		description:
			"Any Iqidis user you share the above URL with can view this chat",
		icon: <GlobeIcon />,
	},
];

export function VisibilitySelector({
	chatId,
	className,
	selectedVisibilityType,
	showInMobileMenu = false,
}: {
	chatId: string;
	selectedVisibilityType: VisibilityType;
	showInMobileMenu?: boolean;
	mode?: "menu" | "header";
} & React.ComponentProps<typeof Button>) {
	const [open, setOpen] = useState(false);

	const { visibilityType, setVisibilityType } = useChatVisibility({
		chatId,
		initialVisibility: selectedVisibilityType,
	});

	const selectedVisibility = useMemo(
		() => visibilities.find((visibility) => visibility.id === visibilityType),
		[visibilityType],
	);

	// If showing in mobile menu, render as a simple button that cycles through options
	if (showInMobileMenu) {
		return (
			<Button
				variant="ghost"
				className={cn(
					"w-full justify-start px-2 py-1.5 h-auto text-sm transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300",
				)}
				onClick={() => {
					// Toggle between private and public
					const newVisibility =
						visibilityType === "private" ? "public" : "private";
					setVisibilityType(newVisibility);
				}}
			>
				{selectedVisibility?.icon && (
					<span className="h-4 w-4 mr-2">{selectedVisibility.icon}</span>
				)}
				{selectedVisibility?.label}
			</Button>
		);
	}

	return (
		<DropdownMenu open={open} onOpenChange={setOpen}>
			<DropdownMenuTrigger
				asChild
				className={cn(
					"w-fit transition-all duration-200 focus-visible:ring-0 focus:outline-none focus:ring-0 border-0 outline-none ring-0",
					className,
				)}
			>
				<Button
					variant="ghost"
					className="hidden md:flex md:px-3 md:h-[36px] transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-800 dark:hover:text-gray-200 data-[state=open]:bg-gray-100 data-[state=open]:dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus-visible:ring-0 focus:outline-none outline-none ring-0 focus:ring-0 border border-gray-200 dark:border-gray-700"
				>
					{selectedVisibility?.icon}
					<span className="hidden xl:inline text-sm">
						{selectedVisibility?.label}
					</span>
					<ChevronDownIcon />
				</Button>
			</DropdownMenuTrigger>

			<DropdownMenuContent align="start" className="min-w-[300px]">
				{visibilities.map((visibility) => (
					<DropdownMenuItem
						key={visibility.id}
						onSelect={() => {
							setVisibilityType(visibility.id);
							setOpen(false);
						}}
						className="gap-4 group/item flex flex-row justify-between items-center privateIconDropdown"
						data-active={visibility.id === visibilityType}
					>
						<div className="flex flex-col gap-1 items-start">
							{visibility.label}
							{visibility.description && (
								<div className="text-xs text-muted-foreground privateDesc">
									{visibility.description}
								</div>
							)}
						</div>
						<div className="text-foreground dark:text-foreground opacity-0 group-data-[active=true]/item:opacity-100">
							<CheckCircleFillIcon />
						</div>
					</DropdownMenuItem>
				))}
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
