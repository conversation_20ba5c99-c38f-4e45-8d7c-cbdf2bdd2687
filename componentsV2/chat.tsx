"use client";

import * as Sentry from "@sentry/nextjs";
import type { Message as AIMessage, Attachment, ChatRequestOptions } from "ai";
import { useChat } from "ai/react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import useSWR, { useSWRConfig } from "swr";
import { useUser } from "@/contexts/UserContext";
import { useBlockSelector } from "@/hooks/use-block";
import { usePendingChatInput } from "@/hooks/use-pending-chat-input";
import type { Vote } from "@/lib/db/schema";
import type { ExtendedMessage } from "@/lib/types";
import { fetcher } from "@/lib/utils";
import { createErrorMessage } from "@/lib/utils/errorUtils";
import { Logger } from "@/lib/utils/Logger";
import { Block } from "@/componentsV2/block";
import { ChatPanel } from "@/componentsV2/chat-panel";
import { ChatSidebar } from "@/componentsV2/chat-sidebar";
import type { VisibilityType } from "@/componentsV2/visibility-selector";
import { useAutoResume } from "@/hooks/use-auto-resume";
import { ChatHeader } from "@/componentsV2/chat-header";

export function Chat({
	id,
	initialMessages,
	selectedModelId,
	selectedVisibilityType,
	isReadonly,
	title,
	isFreeActiveSubscription,
	autoResume = true,
}: {
	id: string;
	initialMessages: Array<ExtendedMessage>;
	selectedModelId: string;
	selectedVisibilityType: VisibilityType;
	isReadonly: boolean;
	title?: string;
	isFreeActiveSubscription?: boolean;
	autoResume?: boolean;
}) {
	const { mutate } = useSWRConfig();
	const [currentModelId, _setCurrentModelId] = useState(selectedModelId);
	const [currentQuery, setCurrentQuery] = useState("");
	const [isThinking, setIsThinking] = useState(false);
	const [isGotMessageContent, setIsGotMessageContent] = useState(false);

	const user = useUser();

	const { mutate: sideBarMutate } = useSWR(
		"/api/chat-org/sidebar-data",
		fetcher,
	);

	const {
		messages: aiMessages,
		setMessages,
		handleSubmit,
		input,
		setInput,
		append,
		isLoading,
		stop,
		reload,
		status,
	} = useChat({
		id,
		body: {
			id,
			modelId: selectedModelId,
			isTyped: true,
		},
		initialMessages: initialMessages as AIMessage[],
		experimental_throttle: 0,
		sendExtraMessageFields: true,
		onResponse: (response) => {
			response
				.clone()
				.text()
				.then((text) => {
					try {
						const events = text.split("\n").filter(Boolean);

						events.forEach((event) => {
							if (event.startsWith("f:")) {
								const finalMessage = JSON.parse(event.slice(2));
								if (finalMessage.metadata) {
									setMessages((prevMessages: ExtendedMessage[]) => {
										const newMessages = [...prevMessages];
										const msgIndex = newMessages.findIndex(
											(msg) => msg.id === finalMessage.messageId,
										);

										if (msgIndex !== -1) {
											newMessages[msgIndex] = {
												...newMessages[msgIndex],
												metadata: {
													...(newMessages[msgIndex].metadata || {}),
													...finalMessage.metadata,
												},
											};
										}

										return newMessages;
									});
								}
							}
						});
					} catch (error) {
						Logger.error("Failed to process stream:", error);
					}
				});
		},
		onError: (error) => {
			setIsThinking(false);

			// Capture error with Sentry
			Sentry.captureException(error, {
				tags: {
					errorId: "chat-error",
					chatId: id,
					modelId: currentModelId,
				},
			});

			// Always add a new error message to the chat
			setMessages((prevMessages: ExtendedMessage[]) => {
				const lastUserMessage = [...prevMessages]
					.reverse()
					.find((m) => m.role === "user");
				const lastUserMessageId = lastUserMessage?.id;
				const errorMessage = createErrorMessage(
					error,
					lastUserMessageId,
					prevMessages,
				);

				return [...prevMessages, errorMessage];
			});
		},
		onFinish: () => {
			setIsThinking(false);
			Logger.debug("Chat: AI response completed");
		},
	});

	useEffect(() => {
		if (status === "streaming" && messages.length <= 2) {
			sideBarMutate();
		}
	}, [status]);

	const messages = aiMessages as ExtendedMessage[];

	// Add the useAutoResume hook
	const { isPolling, enableResumeThinking } = useAutoResume({
		chatId: id,
		messages,
		isAutoResumeEnabled: autoResume,
		setIsThinking,
		setMessages,
		isCurrentlyThinking: isThinking,
		setIsGotMessageContent,
	});

	const customHandleSubmit = (
		event?: { preventDefault?: () => void },
		chatRequestOptions?: ChatRequestOptions,
	) => {
		setCurrentQuery(input);
		setIsThinking(true);
		handleSubmit(event, chatRequestOptions);
		// Check if this is a new chat (no messages yet)
		const isNewChat = messages.length === 0;

		if (isNewChat) {
			// Create placeholder in sidebar after submitting
			mutate(
				"/api/chat-org/sidebar-data",
				(currentData: any) => {
					if (!currentData) return currentData;

					// Check if chat with this ID already exists
					const chatExists = currentData.chats.some(
						(chat: any) => chat.id === id,
					);
					if (chatExists) return currentData;

					// Create a placeholder chat with loading state
					const placeholderChat = {
						id,
						title: "Creating new chat...",
						createdAt: new Date().toISOString(),
						updatedAt: new Date().toISOString(),
						isLoading: true,
						visibility: selectedVisibilityType,
						tags: [],
					};

					// Add to the beginning of chats array
					return {
						...currentData,
						chats: [placeholderChat, ...(currentData.chats || [])],
					};
				},
				false,
			); // Don't revalidate immediately
		}
	};
	// Debug effect for monitoring messages changes
	useEffect(() => {
		if (aiMessages.length > 0) {
		}
	}, [aiMessages]);

	// Create a ref to store the submitForm function
	const submitFormRef = useRef<((useLegacyModel?: boolean) => void) | null>(
		null,
	);

	// Create a ref to store the scrollToBottom function
	const scrollToBottomRef = useRef<((forceImmediate?: boolean) => void) | null>(
		null,
	);

	// Add a callback to store the submitForm function
	const setSubmitFormRef = useCallback(
		(submitFn: (useLegacyModel?: boolean) => void) => {
			submitFormRef.current = submitFn;
		},
		[],
	);

	// Add a retry handler function that can retry a specific user message
	const handleRetry = useCallback(
		(userMessageId?: string, useLegacyModel?: boolean) => {
			let messageToRetry: ExtendedMessage | undefined;

			if (userMessageId) {
				// Find the specific user message by ID
				messageToRetry = messages.find(
					(m) => m.id === userMessageId && m.role === "user",
				);
			} else {
				// Fall back to finding the last user message
				messageToRetry = [...messages].reverse().find((m) => m.role === "user");
			}

			if (messageToRetry?.content && submitFormRef.current) {
				setInput(
					typeof messageToRetry.content === "string"
						? messageToRetry.content
						: JSON.stringify(messageToRetry.content),
				);

				// Use requestAnimationFrame to ensure state is updated before submitting
				requestAnimationFrame(() => {
					if (submitFormRef.current) {
						submitFormRef.current(useLegacyModel);
					}
				});
			}
		},
		[messages, setInput],
	);

	const { data: votes } = useSWR<Array<Vote>>(
		`/api/vote?chatId=${id}`,
		fetcher,
		{
			revalidateOnFocus: false,
			dedupingInterval: 600000, // large deduping interval to prevent too many requests
			revalidateIfStale: false,
		},
	);
	// Debug effect for monitoring messages changes
	useEffect(() => {
		if (aiMessages.length > 0) {
		}
	}, [aiMessages]);

	// Check for pending chat input from prompt explorer
	usePendingChatInput(setInput);

	// Expose setInput to window for external components to use
	useEffect(() => {
		if (typeof window !== "undefined") {
			(window as any).__CHAT_INPUT_SETTER__ = setInput;

			// Cleanup when component unmounts
			return () => {
				delete (window as any).__CHAT_INPUT_SETTER__;
			};
		}
	}, [setInput]);

	// Expose setMessages to window for external components to use
	useEffect(() => {
		if (typeof window !== "undefined") {
			(window as any).__CHAT_MESSAGES_SETTER__ = setMessages;

			// Cleanup when component unmounts
			return () => {
				delete (window as any).__CHAT_MESSAGES_SETTER__;
			};
		}
	}, [setMessages]);

	// Expose setMessages to window for external components to use
	useEffect(() => {
		if (typeof window !== "undefined") {
			(window as any).__CHAT_MESSAGES_SETTER__ = setMessages;

			// Cleanup when component unmounts
			return () => {
				delete (window as any).__CHAT_MESSAGES_SETTER__;
			};
		}
	}, [setMessages]);

	// Expose setMessages to window for external components to use
	useEffect(() => {
		if (typeof window !== "undefined") {
			(window as any).__CHAT_MESSAGES_SETTER__ = setMessages;

			// Cleanup when component unmounts
			return () => {
				delete (window as any).__CHAT_MESSAGES_SETTER__;
			};
		}
	}, [setMessages]);

	const [attachments, setAttachments] = useState<Array<Attachment>>([]);
	const isBlockVisible = useBlockSelector((state) => state.isVisible);

	// Check if chat is getting too long
	const isChatTooLong = useMemo(() => {
		const assistantMessagesCount = messages.filter(
			(m) => m.role === "assistant",
		).length;
		const messagesSize = JSON.stringify(messages).length / (1024 * 1024); // Size in MB
		const attachmentCount = messages.reduce(
			(count, message) =>
				count + (message.experimental_attachments?.length || 0),
			0,
		);

		setIsGotMessageContent(
			assistantMessagesCount > 0 &&
				messages[messages.length - 1].role === "assistant" &&
				!!messages[messages.length - 1].content,
		);

		return (
			assistantMessagesCount >= 20 || messagesSize >= 3 || attachmentCount >= 15
		);
	}, [messages]);

	const handleEdit = (newContent: string, useLegacyModel?: boolean) => {
		if (newContent.trim() === "" || !submitFormRef.current) return;
		setInput(newContent);
		requestAnimationFrame(() => {
			if (submitFormRef.current) {
				submitFormRef.current(useLegacyModel);
			}
		});
	};

	return (
		<>
			<div className="w-full h-dvh max-h-dvh max-w-full min-h-0 chat-background-light dark:chat-background-dark safari-height-fix marker">
				<ChatHeader
					chatId={id}
					selectedModelId={currentModelId}
					selectedVisibilityType={selectedVisibilityType}
					isReadonly={isReadonly}
					title={title}
					isFreeActiveSubscription={isFreeActiveSubscription}
					showWarning={isChatTooLong}
				/>

				<div className="flex flex-1 pl-4 pr-4 md:pl-0 md:pr-4 pb-1 sm:pb-2 md:pb-4 h-[calc(100%-72px)] w-full overflow-hidden">
					<div className="flex flex-1 flex-row max-h-full border-0 sm:border rounded-none sm:rounded-lg border-gray-200 dark:border-slate-700 w-full overflow-hidden">
						<div className="hidden xl:block">
							<ChatSidebar user={user} />
						</div>
						<ChatPanel
							chatId={id}
							isLoading={isLoading || enableResumeThinking}
							votes={votes}
							messages={messages}
							setMessages={setMessages}
							reload={reload}
							isReadonly={isReadonly}
							isBlockVisible={isBlockVisible}
							onRetry={handleRetry}
							scrollToBottomRef={scrollToBottomRef}
							isProcessing={isThinking}
							hideThinking={isGotMessageContent}
							query={currentQuery}
							onEdit={handleEdit}
							input={input}
							setInput={setInput}
							handleSubmit={customHandleSubmit}
							stop={stop}
							attachments={attachments}
							setAttachments={setAttachments}
							append={append}
							setSubmitFormRef={setSubmitFormRef}
							selectedModelId={currentModelId}
							chatTitle={title}
						/>
					</div>
				</div>
			</div>

			<Block
				chatId={id}
				input={input}
				setInput={setInput}
				handleSubmit={customHandleSubmit}
				isLoading={isLoading || enableResumeThinking}
				stop={stop}
				attachments={attachments}
				setAttachments={setAttachments}
				append={append}
				messages={messages}
				filteredMessages={messages}
				setMessages={setMessages}
				reload={reload}
				votes={votes}
				isReadonly={isReadonly}
				selectedModelId={currentModelId}
			/>
		</>
	);
}
