import type { ChatRequestOptions } from "ai";
import type { ExtendedMessage } from "@/lib/types";
import { PreviewMessage } from "@/componentsV2/message";
import { useScrollToBottom } from "@/componentsV2/use-scroll-to-bottom";
import React, { memo, useEffect, useState } from "react";
import type { Vote } from "@/lib/db/schema";
import equal from "fast-deep-equal";
import { ScrollButton } from "@/componentsV2/scroll-button";
import { BookOpenTextIcon, NotebookTextIcon } from "lucide-react";
import { RagThinkingDisplay } from "@/componentsV2/rag-thinking-display";
import { AnimatePresence, motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { usePlaybook } from "@/componentsV2/global-playbook-provider";

interface MessagesProps {
	chatId: string;
	isLoading: boolean;
	votes: Array<Vote> | undefined;
	messages: Array<ExtendedMessage>;
	setMessages: (
		messages:
			| ExtendedMessage[]
			| ((messages: ExtendedMessage[]) => ExtendedMessage[]),
	) => void;
	reload: (
		chatRequestOptions?: ChatRequestOptions,
	) => Promise<string | null | undefined>;
	isReadonly: boolean;
	isBlockVisible: boolean;
	onRetry?: (userMessageId?: string) => void;
	scrollToBottomRef?: React.MutableRefObject<
		((forceImmediate?: boolean) => void) | null
	>;
	isProcessing: boolean;
	query: string;
	rewrittenQuery?: string;
	hideThinking: boolean;
	onEdit?: (newContent: string) => void;
}

function PureMessages({
	chatId,
	isLoading,
	votes,
	messages,
	setMessages,
	reload,
	isReadonly,
	isBlockVisible,
	onRetry,
	scrollToBottomRef,
	isProcessing,
	query,
	rewrittenQuery,
	hideThinking,
	onEdit,
}: MessagesProps) {
	const [
		messagesContainerRef,
		messagesEndRef,
		isScrolledUp,
		setIsScrolledUp,
		scrollToBottom,
	] = useScrollToBottom<HTMLDivElement>();

	const [hasAttachments, setHasAttachments] = useState(false);

	const { openPlaybook, closePlaybook, isPlaybookOpen } = usePlaybook();

	// Check if there are attachments in the input
	useEffect(() => {
		const checkForAttachments = () => {
			const attachmentsContainer = document.querySelector(
				".multimodal-attachments",
			);
			setHasAttachments(
				!!attachmentsContainer && attachmentsContainer.children.length > 0,
			);
		};

		// Check initially and set up a mutation observer to detect changes
		checkForAttachments();

		const observer = new MutationObserver(checkForAttachments);
		observer.observe(document.body, { childList: true, subtree: true });

		return () => observer.disconnect();
	}, []);

	const hasStreamStarted =
		messages.length > 0 &&
		messages[messages.length - 1].role === "assistant" &&
		messages[messages.length - 1].content;

	return (
		<>
			<div className="flex flex-col flex-1 w-full min-h-0 relative">
				{/* playbook */}
				<div className="flex items-end justify-end absolute top-[8px] sm:top-[12px] lg:top-[15px] right-[8px] sm:right-[15px] lg:right-[30px] z-[40] playbookBtn">
					<Button
						type="button"
						className="h-8 w-8 sm:h-10 sm:w-auto shadow-md transition-all duration-200 bg-gradient-to-r from-purple-500 to-pink-500 p-1.5 sm:p-2"
						disabled={false}
						onClick={() => (isPlaybookOpen ? closePlaybook() : openPlaybook())}
					>
						<div className="flex items-center gap-1 sm:gap-2">
							{isPlaybookOpen ? (
								<BookOpenTextIcon className="h-3 w-3 sm:h-4 sm:w-4" />
							) : (
								<NotebookTextIcon className="h-3 w-3 sm:h-4 sm:w-4" />
							)}
							<span className="mobileHide text-xs sm:text-sm">Playbook</span>
						</div>
					</Button>
				</div>

				<div
					ref={messagesContainerRef}
					className={`flex flex-col min-w-0 gap-2 sm:gap-4 md:gap-8 flex-1 min-h-0 overflow-y-scroll pt-2 sm:pt-3 relative lg:mt-0 mt-12 ${
						hasAttachments ? "pb-32" : "pb-2 sm:pb-4"
					}`}
				>
					{messages.map((message, index) => {
						return (
							<React.Fragment key={message.id}>
								{/* Only render PreviewMessage if content is present or not an assistant */}
								{(message.content || message.role !== "assistant") && (
									<AnimatePresence>
										<motion.div
											className="w-full mx-auto max-w-full sm:max-w-4xl px-1 sm:px-2 md:px-4 lg:px-6 group/message"
											initial={{ y: 5, opacity: 0 }}
											animate={{ y: 0, opacity: 1 }}
											data-role={message.role}
											data-message-id={message.id}
											data-has-metadata={Boolean(message.metadata)}
											data-has-internet-results={Boolean(
												message.metadata?.internetResults,
											)}
											data-has-error={message.metadata?.error}
											data-has-relevant-images={Boolean(
												message.metadata?.relevantImages?.length,
											)}
										>
											<PreviewMessage
												chatId={chatId}
												message={{
													...message,
													metadata: {
														internetResults: message.metadata?.internetResults,
														chainOfThoughts: message.metadata?.chainOfThoughts,
														relevantImages: message.metadata?.relevantImages,
														error: message.metadata?.error,
														errorMessage: message.metadata?.errorMessage,
														...message.metadata,
													},
													experimental_attachments:
														message.experimental_attachments?.map(
															(attachment) => ({
																...attachment,
																// Ensure fromPlaybook flag is preserved
																fromPlaybook: attachment.fromPlaybook,
															}),
														),
												}}
												isLoading={isLoading && messages.length - 1 === index}
												vote={votes?.find(
													(vote) => vote.messageId === message.id,
												)}
												setMessages={setMessages}
												reload={reload}
												isReadonly={isReadonly}
												onRetry={onRetry}
												onEdit={onEdit}
												isMessageLoading={isLoading}
												isLastMessage={index === messages.length - 1}
											/>
										</motion.div>
									</AnimatePresence>
								)}
							</React.Fragment>
						);
					})}
					{isLoading && !hideThinking && (
						<div className="w-full max-w-full sm:max-w-4xl mx-auto px-1 sm:px-2 md:px-4 my-1 sm:my-2">
							<RagThinkingDisplay
								chatId={chatId}
								isLoading={isLoading}
								isProcessing={isProcessing}
								query={query}
								hideThinking={hideThinking}
							/>
						</div>
					)}
					<div ref={messagesEndRef} className="shrink-0" />
				</div>
				{/* <div className="blur-overlay" id="blurOverlay"></div> */}
				{/* Center the button horizontally with fixed position */}
				<div className="absolute left-1/2 -translate-x-1/2 bottom-0 z-[48]">
					<ScrollButton
						containerRef={messagesContainerRef}
						isVisible={isScrolledUp}
					/>
				</div>
			</div>
		</>
	);
}

export const Messages = memo(PureMessages, (prevProps, nextProps) => {
	if (prevProps.isBlockVisible && nextProps.isBlockVisible) return true;

	if (prevProps.isLoading !== nextProps.isLoading) return false;
	if (prevProps.isLoading && nextProps.isLoading) return false;
	if (prevProps.messages.length !== nextProps.messages.length) return false;
	if (!equal(prevProps.votes, nextProps.votes)) return false;
	// Add metadata comparison
	if (
		!equal(
			prevProps.messages.map((m) => m.metadata),
			nextProps.messages.map((m) => m.metadata),
		)
	)
		return false;

	return true;
});
