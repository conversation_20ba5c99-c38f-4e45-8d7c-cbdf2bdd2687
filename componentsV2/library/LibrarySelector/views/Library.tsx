import { useRequest } from "ahooks";
import { useAtom } from "jotai";
import { Database, Folder, Loader2 } from "lucide-react";
import { getFolders } from "@/componentsV2/library/request/folders";
import { useVirtualPath } from "@/componentsV2/library/LibrarySelector/hooks";
import { LibraryFilterAtom } from "@/componentsV2/library/LibrarySelector/store";

export default function LibraryView() {
	const { setPath, path } = useVirtualPath();
	const { data: { folders = [], rootFolder, rootFolderId } = {}, loading } =
		useRequest(
			() => {
				const queryString = new URLSearchParams({
					status: "AVAILABLE",
				});
				return getFolders(queryString.toString());
			},
			{
				ready: path === "/library/folder" || path === "/library",
				cacheKey: "library-folders",
				cacheTime: 10000,
			},
		);
	const [, setLibraryFilter] = useAtom(LibraryFilterAtom);

	if (loading) {
		return (
			<div className="flex items-center justify-center py-8">
				<Loader2 className="h-6 w-6 animate-spin" />
			</div>
		);
	}

	return (
		<div className="flex flex-col gap-3 py-1 min-h-[234px] max-h-[580px] overflow-y-auto md:px-6 px-3">
			<div
				className="flex cursor-pointer gap-3 hover:bg-muted py-3 px-3 rounded-lg border border-border items-center transition-colors"
				onClick={() => {
					setPath("/library/folder");
					setLibraryFilter((prev) => {
						return {
							...prev,
							currentFolderId: rootFolderId,
						};
					});
				}}
			>
				<Database className="size-5 text-muted-foreground flex-shrink-0" />
				<div className="flex flex-col gap-1 flex-1">
					<div className="text-sm font-medium">All Documents</div>
					<div className="text-xs text-muted-foreground font-medium">
						Browse entire library
					</div>
				</div>
				<div className="text-sm text-muted-foreground font-medium">
					{rootFolder?.documentCount ?? "-"} file
					{+rootFolder?.documentCount === 1 ? "" : "s"}
				</div>
			</div>
			<div className="text-xs text-muted-foreground font-medium uppercase tracking-wider">
				FOLDERS
			</div>
			{folders?.length === 0 ? (
				<div className="text-center py-8">
					<p className="text-sm text-muted-foreground mb-2">No folders found</p>
					<p className="text-xs text-muted-foreground">
						Create folders in your library to organize documents
					</p>
				</div>
			) : (
				folders?.map((folder) => (
					<div
						key={folder.folderId}
						className="flex cursor-pointer gap-3 hover:bg-muted py-3 px-3 rounded-lg border border-border items-center transition-colors"
						onClick={() => {
							setPath("/library/folder");
							setLibraryFilter((prev) => {
								return {
									...prev,
									currentFolderId: folder.folderId,
								};
							});
						}}
					>
						<Folder className="size-5 text-muted-foreground flex-shrink-0" />
						<div className="flex flex-col gap-1 flex-1">
							<div className="text-sm font-medium">{folder.folderName}</div>
							<div className="text-xs text-muted-foreground font-medium">
								{folder.folderDescription || "No description"}
							</div>
						</div>
						<div className="text-sm text-muted-foreground font-medium">
							{folder.documentCount} file
							{+folder.documentCount === 1 ? "" : "s"}
						</div>
					</div>
				))
			)}
		</div>
	);
}
