import { useRequest } from "ahooks";
import { Attachment } from "ai";
import { formatDistanceToNow } from "date-fns";
import { useSetAtom } from "jotai";
import { uniqBy } from "lodash-es";
import {
	FileText,
	FolderOpen,
	Search,
	Star,
	Loader2,
	Download,
} from "lucide-react";
import { useTheme } from "next-themes";
import { Dispatch, SetStateAction, useRef, useState } from "react";
import { toast } from "sonner";
import { cn, generateUUID } from "@/lib/utils";
import {
	favoriteDocument,
	getFavoritesDocuments,
	getRecentlyDocuments,
} from "@/componentsV2/library/request/document";
import { formatSize } from "@/componentsV2/library/utils/document";
import { useVirtualPath } from "@/componentsV2/library/LibrarySelector/hooks";
import { LibrarySelectorDialogAtom } from "@/componentsV2/library/LibrarySelector/store";
import {
	Button,
	Checkbox,
	Input,
	Radio,
	Space,
	Spin,
	Typography,
	type InputRef,
} from "antd";

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;
const TypographyText = Typography.Text;

export default function RecentView(props: {
	attachments: Array<Attachment>;
	setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
}) {
	const fileNameRef = useRef<InputRef>(null);
	const [fileName, setFileName] = useState<string>("");
	const [recentType, setRecentType] = useState<string>("recent");
	const { setPath } = useVirtualPath();
	const setLibrarySelectorDialog = useSetAtom(LibrarySelectorDialogAtom);
	const [refresh, setRefresh] = useState(0);
	const { theme: themeMode } = useTheme();
	const isFirstLoad = useRef(true);
	// Add state for selected items
	const [selectedItems, setSelectedItems] = useState<string[]>([]);
	const [selectedItemsCache, setSelectedItemsCache] = useState<any[]>([]);
	const { data, loading } = useRequest(
		async () => {
			const queryString = new URLSearchParams({
				current: "1",
				pageSize: "1000",
				status: "AVAILABLE",
				favorite: "1",
				includeRecent: "1",
			});
			if (fileName) {
				queryString.append("filename", fileName);
			}

			const [recently, favorites] = await Promise.all([
				getRecentlyDocuments(fileName ? `fileName=${fileName}` : undefined),
				getFavoritesDocuments(queryString.toString()),
			]);
			return {
				recentlyDocuments: recently.documents ?? [],
				favoritesDocuments: favorites.documents ?? [],
				recentlyDocumentActionsMap: recently.documentActionsMap ?? {},
			};
		},
		{
			refreshDeps: [fileName, recentType, refresh],
			throttleWait: 100,
			throttleTrailing: false,
			onSuccess: () => {
				isFirstLoad.current = false;
			},
		},
	);

	const { recentlyDocuments, favoritesDocuments, recentlyDocumentActionsMap } =
		data ?? {
			recentlyDocuments: [],
			favoritesDocuments: [],
			recentlyDocumentActionsMap: {},
		};

	const lists =
		recentType === "recent"
			? recentlyDocuments
			: favoritesDocuments.map((doc: any) => {
					return {
						...doc,
						usedCount: recentlyDocumentActionsMap[doc.id]?.CHAT ?? 0,
						createdAt:
							recentType === "recent"
								? recentlyDocumentActionsMap[doc.id]?.createdAt
								: doc.updatedAt,
					};
				});

	const { runAsync: runFavoriteDocument } = useRequest(
		async (id: string, isFavorite: boolean) => {
			const res = await favoriteDocument(id, isFavorite);
			if (res) {
				toast.success(
					isFavorite
						? "Added to favorites successfully"
						: "Removed from favorites successfully",
				);
				setRefresh(refresh + 1);
			} else {
				toast.error(
					isFavorite
						? "Failed to add to favorites"
						: "Failed to remove from favorites",
				);
			}
		},
		{
			manual: true,
			throttleWait: 1000,
			throttleTrailing: false,
		},
	);

	return (
		<div className="flex flex-col gap-5 pt-2">
			<div className="flex items-center gap-3 flex-wrap md:px-6 px-3">
				<div className="w-full">
					<Input
						placeholder="Search documents..."
						ref={fileNameRef}
						prefix={<Search className="size-4 text-gray-medium" />}
						allowClear
						onPressEnter={() => {
							setFileName(fileNameRef.current?.input?.value ?? "");
						}}
						onBlur={() => {
							setFileName(fileNameRef.current?.input?.value ?? "");
						}}
						onClear={() => {
							setFileName("");
						}}
					/>
				</div>
				<div>
					<RadioGroup
						buttonStyle="solid"
						value={recentType}
						onChange={(e) => {
							setRecentType(e.target.value);
						}}
					>
						<RadioButton value="recent">
							Recent ({recentlyDocuments.length})
						</RadioButton>
						<RadioButton value="favorites">
							Favorites ({favoritesDocuments.length})
						</RadioButton>
					</RadioGroup>
				</div>
				<div className="flex-1 flex justify-end">
					<Button
						type="primary"
						icon={<Download size={16} />}
						disabled={selectedItems.length === 0}
						onClick={() => {
							const selectedDocs = selectedItemsCache
								.filter((doc) => selectedItems.includes(doc.id))
								.filter(
									(doc) =>
										!props.attachments.some(
											(att) => att._attachment_id === doc.id,
										),
								)
								.map((doc) => ({
									document_id: generateUUID(),
									_attachment_id: doc.id,
									contentType: doc.mime,
									name: doc.originalName,
									url: `https://iqidis-artifact.s3.us-east-1.amazonaws.com/${doc.storageKey}`,
								}));

							if (selectedDocs.length > 0) {
								props.setAttachments(
									uniqBy(
										[...props.attachments, ...selectedDocs],
										"_attachment_id",
									),
								);
								setLibrarySelectorDialog({
									open: false,
									viewVirtualPath: "",
								});
							} else {
								toast.error("All selected files are already attached");
							}

							// Reset selection
							setSelectedItems([]);
							setSelectedItemsCache([]);
						}}
					>
						Attach Selected ({selectedItems.length})
					</Button>
				</div>
			</div>
			<div className="min-h-[200px] max-h-[400px] overflow-y-auto overflow-x-hidden pb-5 border-b flex flex-col gap-2 md:px-6 px-3">
				{isFirstLoad.current && loading ? (
					<div className="flex justify-center items-center h-full min-h-[200px]">
						<Spin />
					</div>
				) : lists.length > 0 ? (
					<>
						{lists.map((doc: any) => {
							const isSelected = selectedItems.includes(doc.id);
							const isDisabled = props.attachments.some(
								(attachment: any) => attachment._attachment_id === doc.id,
							);

							return (
								<div
									key={doc.id}
									className={`flex items-start gap-2 flex-col sm:flex-row border rounded-md md:py-2 py-3 px-2 sm:px-4 ${themeMode === "dark" ? "hover:bg-gray-800 border-gray-700" : "hover:bg-gray-50 border-gray-200"}`}
								>
									<div className="w-full">
										<div className="flex">
											<Checkbox
												checked={isSelected}
												disabled={isDisabled}
												onChange={(e) => {
													if (e.target.checked) {
														setSelectedItems([...selectedItems, doc.id]);
														setSelectedItemsCache((prev) =>
															uniqBy([...prev, doc], "id"),
														);
													} else {
														setSelectedItems(
															selectedItems.filter((id) => id !== doc.id),
														);
													}
												}}
												className="mt-1 min-w-[24px]"
											/>
											<FileText
												size={24}
												className="text-gray-medium dark:text-white mt-1 min-w-[24px]"
											/>
											<div className="flex items-center gap-2 w-full md:w-fit justify-between ml-1">
												<TypographyText
													ellipsis={{ tooltip: true }}
													className={`recentFavFileName truncate font-medium ${themeMode === "dark" ? "text-white" : "text-black"}`}
												>
													{doc.originalName}
												</TypographyText>
												<Star
													className={cn(
														"size-4 cursor-pointer hover:text-yellow-700",
														doc.isFavorite && "text-yellow-300 fill-current",
													)}
													onClick={() => {
														runFavoriteDocument(doc.id, !doc.isFavorite);
													}}
												/>
											</div>
										</div>
										<div className="flex flex-col gap-0.5 flex-1">
											<Space
												className={`text-xs gap-4 my-2 flex-wrap ${themeMode === "dark" ? "text-gray-300" : "text-gray-medium"}`}
												split={
													<span
														className={
															themeMode === "dark"
																? "text-gray-300"
																: "text-gray-medium"
														}
													>
														{" "}
														•{" "}
													</span>
												}
											>
												<span className="text-nowrap">
													{formatSize(doc.size)}
												</span>
												<span className="text-nowrap">
													{doc.createdAt
														? formatDistanceToNow(
																new Date(doc.createdAt.replace(/Z$/, "") + "Z"),
																{ addSuffix: true },
															)
														: ""}
												</span>
												<span className="text-nowrap">
													Used {doc.usedCount} times
												</span>
											</Space>
											<div
												className={`flex items-center gap-1 text-xs md:mb-0 mb-3 ${themeMode === "dark" ? "text-gray-300" : "text-gray-medium"}`}
											>
												<FolderOpen className="size-4" />
												<span>
													{doc.folderId === null
														? "recent"
														: doc.folderName === "root"
															? "default"
															: doc.folderName}
												</span>
											</div>
										</div>
									</div>
									<Button
										disabled={isDisabled}
										onClick={() => {
											props.setAttachments(
												uniqBy(
													[
														...props.attachments,
														{
															document_id: generateUUID(),
															_attachment_id: doc.id,
															contentType: doc.mime,
															name: doc.originalName,
															url: `https://iqidis-artifact.s3.us-east-1.amazonaws.com/${doc.storageKey}`,
														},
													],
													"_attachment_id",
												),
											);
											setLibrarySelectorDialog({
												open: false,
												viewVirtualPath: "",
											});
										}}
										type="primary"
									>
										Attach
									</Button>
								</div>
							);
						})}
					</>
				) : (
					<div className="flex justify-center items-center h-full min-h-[200px]">
						No documents found
					</div>
				)}
			</div>
			<div className="flex items-center gap-4 md:px-6 px-3">
				<Button className="flex-1" onClick={() => setPath("/library")}>
					Browse Library
				</Button>
				<Button className="flex-1" onClick={() => setPath("/upload")}>
					Upload New
				</Button>
			</div>
		</div>
	);
}
