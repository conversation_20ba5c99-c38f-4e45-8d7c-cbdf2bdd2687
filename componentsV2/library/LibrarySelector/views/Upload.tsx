import { useMemoizedFn, useRequest } from "ahooks";
import type { Attachment } from "ai";
import { get, uniqBy } from "lodash-es";
import { useParams } from "next/navigation";
import { useTheme } from "next-themes";
import { Dispatch, SetStateAction, useMemo, useState } from "react";
import { generateUUID } from "@/lib/utils";
import { getDocuments } from "@/componentsV2/library/request/document";
import { prepareFiles } from "@/componentsV2/library/request/prepareFiles";
import { UploadDragger } from "@/componentsV2/library/Upload/UploadDragger";
import { useVirtualPath } from "@/componentsV2/library/LibrarySelector/hooks";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

export default function UploadView(props: {
	closeDialog: () => void;
	attachments: Array<Attachment>;
	setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
}) {
	const { closeDialog } = props;
	const params = useParams();
	const chatId = params.id as string;
	const { setPath } = useVirtualPath();
	const [fileList, setFileList] = useState<any[]>([]);
	const [notToLibrary, setnotToLibrary] = useState(false);
	const [uploadStatus, setUploadStatus] = useState<
		"done" | "part-error" | "idle" | "uploading"
	>("idle");

	const { theme: themeMode } = useTheme();

	const checkUploadStatus = useMemoizedFn(() => {
		return fileList.every((it) => it.status === "done")
			? "done"
			: fileList.some((it) => it.status === "error")
				? "part-error"
				: "idle";
	});

	const { runAsync: uploadFiles, loading: uploadLoading } = useRequest(
		async () => {
			if (fileList.length === 0) {
				return;
			}
			setUploadStatus("uploading");
			setFileList(
				fileList.map((it) => ({
					...it,
					status: "uploading",
				})),
			);

			const prepareData = await prepareFiles(
				fileList,
				notToLibrary ? null : undefined,
				"CHAT",
				{ chatId },
			); // if notToLibrary is true, upload to temp folder

			if (prepareData.files.length !== fileList.length) {
				setFileList(
					fileList.map((it) => ({
						...it,
						status: "error",
					})),
				);
				throw new Error("Upload failed");
			}

			await Promise.all(
				fileList.map(async (item: any, index: number) => {
					const uploadUrl = get(
						prepareData,
						`files[${index}].uploadUrl`,
						undefined,
					);
					const docId = get(prepareData, `files[${index}].id`, undefined);
					if (!uploadUrl) {
						item.status = "error";
						// item.error = 'No upload url found'
						setFileList((prev) => {
							const index = prev.findIndex((it) => it.uid === item.uid);
							if (index !== -1) {
								prev[index] = item;
							}
							return [...prev];
						});
						return item;
					}
					const response = await fetch(uploadUrl, {
						method: "PUT",
						headers: {
							"Content-Type": item.mime,
						},
						body: item.originFileObj,
					});
					const fileRecord = {
						...item,
						// message: 'Upload failed',
						status: response.ok ? "done" : "error",
						uploadUrl,
						docId,
					};
					setFileList((prev) => {
						const index = prev.findIndex((it) => it.uid === item.uid);
						if (index !== -1) {
							prev[index] = fileRecord;
						}
						return [...prev];
					});
					return fileRecord;
				}),
			);
			await new Promise((resolve) => setTimeout(resolve, 300));
			setUploadStatus(checkUploadStatus());
		},
		{
			manual: true,
		},
	);

	const { runAsync: retryUpload, loading: retryUploadLoading } = useRequest(
		async (file: any) => {
			let uploadUrl = file.uploadUrl;
			if (!uploadUrl) {
				const data = await prepareFiles(
					[file],
					notToLibrary ? null : undefined,
					"CHAT",
					{ chatId },
				); // if notToLibrary is true, upload to temp folder
				uploadUrl = get(data, `files[0].uploadUrl`, undefined);
				if (!uploadUrl) {
					throw new Error("No upload url found");
				}
			}
			const response = await fetch(file.uploadUrl, {
				method: "PUT",
				headers: {
					"Content-Type": file.mime,
				},
				body: file.originFileObj,
			});
			if (response.ok) {
				file.status = "done";
			} else {
				file.status = "error";
			}
			setFileList((prev) => {
				const index = prev.findIndex((it) => it.uid === file.uid);
				if (index !== -1) {
					prev[index] = file;
				}
				return [...prev];
			});
			await new Promise((resolve) => setTimeout(resolve, 300));
			setUploadStatus(checkUploadStatus());
		},
		{
			manual: true,
		},
	);

	// get attachment data by ids
	const { runAsync: attachmentDocs, loading: getDocumentsByIdsLoading } =
		useRequest(
			async () => {
				const maxRetries = 10;
				let retries = 0;
				let documents: any[] = [];
				while (retries < maxRetries) {
					documents = await getDocuments(fileList.map((it) => it.docId));
					if (documents.every((it: any) => !!it.storageKey)) {
						break;
					}
					await new Promise((resolve) => setTimeout(resolve, 1000));
					retries++;
				}
				if (documents.some((it: any) => !it.storageKey)) {
					throw new Error("Some documents are not uploaded");
				}
				const attachments = documents.map((it: any) => {
					return {
						document_id: generateUUID(),
						_attachment_id: it.id,
						contentType: it.mime,
						name: it.originalName,
						url: `https://iqidis-artifact.s3.us-east-1.amazonaws.com/${it.storageKey}`,
					};
				});
				props.setAttachments(
					uniqBy([...props.attachments, ...attachments], "_attachment_id"),
				);
				setFileList([]);
				setUploadStatus("idle");
				closeDialog();
			},
			{
				manual: true,
				ready: fileList.length > 0,
				retryCount: 3,
				retryInterval: 200,
			},
		);

	return (
		<div className="md:px-6 px-3">
			<UploadDragger
				fileList={fileList}
				setFileList={setFileList}
				uploadLoading={uploadLoading}
				retryUploadLoading={retryUploadLoading}
				uploadStatus={uploadStatus}
				retryUpload={retryUpload}
				themeMode={themeMode}
			/>

			<div className="flex items-center pt-1">
				<div className="py-0.5">
					<Checkbox
						id="not-to-library"
						disabled={uploadStatus !== "idle"}
						checked={notToLibrary}
						onChange={(e) => setnotToLibrary(e.target.checked)}
						label="Do not add document to library"
					/>
				</div>
			</div>
			<div className="mt-2 flex items-center gap-2 justify-center">
				{(uploadStatus === "idle" || uploadStatus === "uploading") && (
					<Button
						className="LibDocdisBtn"
						variant="outline"
						color="danger"
						onClick={() => {
							setPath("");
						}}
						disabled={uploadLoading}
					>
						Discard
					</Button>
				)}
				{(uploadStatus === "idle" || uploadStatus === "uploading") && (
					<Button
						className="dark:disabled:border-0 dark:disabled:bg-[#322155]"
						color="primary"
						variant="default"
						onClick={() => {
							uploadFiles();
						}}
						disabled={fileList.length === 0 || uploadLoading}
					>
						{uploadLoading ? "Uploading..." : "Upload"}
					</Button>
				)}
				{(uploadStatus === "part-error" || uploadStatus === "done") && (
					<Button
						className="LibDocdisBtn"
						color="danger"
						variant="outline"
						onClick={closeDialog}
						disabled={getDocumentsByIdsLoading}
					>
						Close
					</Button>
				)}
				{(uploadStatus === "done" || uploadStatus === "part-error") && (
					<Button
						color="primary"
						className="dark:text-white"
						variant="default"
						onClick={() => {
							attachmentDocs();
						}}
						disabled={
							getDocumentsByIdsLoading ||
							(fileList.length === 0 &&
								fileList.every((it) => it.status === "done"))
						}
					>
						{getDocumentsByIdsLoading ? "Attaching..." : "Attach to chat"}
					</Button>
				)}
			</div>
		</div>
	);
}
