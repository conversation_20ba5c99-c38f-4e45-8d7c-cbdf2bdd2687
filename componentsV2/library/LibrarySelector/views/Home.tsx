import { Clock, Database, Upload } from "lucide-react";
import { useVirtualPath } from "@/componentsV2/library/LibrarySelector/hooks";

export default function Home() {
	const { setPath } = useVirtualPath();

	return (
		<div className="flex flex-col gap-3 py-1 md:px-6 px-3">
			<div
				className="flex cursor-pointer gap-3 hover:bg-muted py-3 px-3 rounded-lg border border-border items-center transition-colors"
				onClick={() => setPath("/library")}
			>
				<Database className="size-5 text-muted-foreground flex-shrink-0" />
				<div className="flex flex-col gap-1">
					<div className="text-sm font-medium">Library Files</div>
					<div className="text-xs text-muted-foreground">
						Select files from your document library
					</div>
				</div>
			</div>
			<div
				className="flex cursor-pointer gap-3 hover:bg-muted py-3 px-3 rounded-lg border border-border items-center transition-colors"
				onClick={() => setPath("/recent")}
			>
				<Clock className="size-5 text-muted-foreground flex-shrink-0" />
				<div className="flex flex-col gap-1">
					<div className="text-sm font-medium">Recent Files</div>
					<div className="text-xs text-muted-foreground">
						Recently used files from conversations
					</div>
				</div>
			</div>
			<div
				className="flex cursor-pointer gap-3 hover:bg-muted py-3 px-3 rounded-lg border border-border items-center transition-colors"
				onClick={() => setPath("/upload")}
			>
				<Upload className="size-5 text-muted-foreground flex-shrink-0" />
				<div className="flex flex-col gap-1">
					<div className="text-sm font-medium">Upload New Files</div>
					<div className="text-xs text-muted-foreground">
						Upload files from your computer
					</div>
				</div>
			</div>
		</div>
	);
}
