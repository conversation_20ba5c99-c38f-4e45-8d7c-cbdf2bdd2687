"use client";

import { use<PERSON><PERSON>, use<PERSON><PERSON><PERSON><PERSON> } from "jotai";
import {
	LibraryFilter<PERSON>tom,
	LibrarySelectorDialogAtom,
	PathTitleMap,
} from "@/componentsV2/library/LibrarySelector/store";
import { ChevronLeft, X as CloseIcon } from "lucide-react";
import Home from "@/componentsV2/library/LibrarySelector/views/Home";
import UploadView from "@/componentsV2/library/LibrarySelector/views/Upload";
import LibraryView from "@/componentsV2/library/LibrarySelector/views/Library";
import LibraryFolderView from "@/componentsV2/library/LibrarySelector/views/LibraryFolder";
import RecentView from "@/componentsV2/library/LibrarySelector/views/Recent";

import type { Dispatch, SetStateAction } from "react";
import type { Attachment } from "ai";

import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogClose,
} from "@/components/ui/dialog";
import { useVirtualPath } from "./hooks";
import { useDeepCompareEffect } from "ahooks";
import { getFolders } from "../request/folders";
import { useRequest } from "ahooks";

function ModalTitle() {
	const { path: viewVirtualPath, setPath } = useVirtualPath();

	return (
		<span className="flex items-center md:text-lg text-base font-semibold">
			{viewVirtualPath !== "" && (
				<ChevronLeft
					className="size-6 cursor-pointer hover:bg-muted rounded-sm mr-1"
					onClick={() => {
						setPath(viewVirtualPath.split("/").slice(0, -1).join("/"));
					}}
				/>
			)}
			{PathTitleMap[viewVirtualPath]}
		</span>
	);
}

interface LibrarySelectorModalProps {
	attachments: Array<Attachment>;
	setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
}

function LibrarySelectorModal(props: LibrarySelectorModalProps) {
	const [dialog, setDialog] = useAtom(LibrarySelectorDialogAtom);

	const closeDialog = () => {
		setDialog({ open: false, viewVirtualPath: "" });
	};

	const { data: { folders = [], rootFolder } = {} } = useRequest(() => {
		const queryString = new URLSearchParams({
			status: "AVAILABLE",
		});
		return getFolders(queryString.toString());
	}, {});

	const setLibraryFilter = useSetAtom(LibraryFilterAtom);

	useDeepCompareEffect(() => {
		setLibraryFilter((prev) => {
			return {
				...prev,
				folders: folders.map((folder) => ({
					folderId: folder.folderId,
					folderName: folder.folderName,
				})),
				rootFolderId: rootFolder?.folderId,
				currentFolderId: rootFolder?.folderId,
			};
		});
	}, [folders, rootFolder]);

	const handleOpenChange = (open: boolean) => {
		setDialog({ ...dialog, open });
	};

	return (
		<Dialog open={dialog.open} onOpenChange={handleOpenChange}>
			<DialogContent className="sm:w-[700px] md:w-[800px] lg:w-[900px] xl:w-[1000px] max-w-[90vw] max-h-[80vh] flex flex-col">
				<DialogHeader className="flex flex-row justify-between items-center">
					<DialogTitle>
						<ModalTitle />
					</DialogTitle>
					<DialogClose asChild>
						<Button variant="ghost" size="icon" className="h-8 w-8">
							<CloseIcon className="h-4 w-4" />
						</Button>
					</DialogClose>
				</DialogHeader>

				<div className="flex-1 min-h-0 overflow-y-auto custom-scrollbar">
					{dialog.viewVirtualPath === "" && <Home />}
					{dialog.viewVirtualPath === "/upload" && (
						<UploadView
							closeDialog={closeDialog}
							attachments={props.attachments}
							setAttachments={props.setAttachments}
						/>
					)}
					{dialog.viewVirtualPath === "/library" && <LibraryView />}
					{dialog.viewVirtualPath === "/library/folder" && (
						<LibraryFolderView
							attachments={props.attachments}
							setAttachments={props.setAttachments}
						/>
					)}
					{dialog.viewVirtualPath === "/recent" && (
						<RecentView
							attachments={props.attachments}
							setAttachments={props.setAttachments}
						/>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

export const useLibrarySelector = (options: LibrarySelectorModalProps) => {
	const [dialog, setDialog] = useAtom(LibrarySelectorDialogAtom);
	return {
		open: dialog.open,
		openDialog: () => {
			setDialog({ ...dialog, open: true, viewVirtualPath: "" });
		},
		closeDialog: () => {
			setDialog({ ...dialog, open: false });
		},
		LibrarySelectorModal: <LibrarySelectorModal {...options} />,
	};
};
