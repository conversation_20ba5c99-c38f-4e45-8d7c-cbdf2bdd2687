"use client";

import { useState } from "react";
import { <PERSON>, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useNotifications } from "@/contexts/NotificationsContext";
import { Loader2 } from "lucide-react";

import { NotificationItem } from "@/componentsV2/notifications/NotificationItem";

export const NotificationsMenu = () => {
	const [open, setOpen] = useState(false);
	const {
		notifications,
		unreadCount,
		markAsRead,
		markAllAsRead,
		filterByType,
		activeFilter,
		setActiveFilter,
		isLoading,
		isRefreshing,
		refreshNotifications,
	} = useNotifications();

	const handleMarkAllRead = async () => {
		await markAllAsRead();
	};

	const handleTabChange = (value: string) => {
		setActiveFilter(value as any);
	};

	const displayNotifications = filterByType(activeFilter);

	return (
		<DropdownMenu open={open} onOpenChange={setOpen}>
			<DropdownMenuTrigger asChild>
				<Button
					variant="ghost"
					size="icon"
					className="relative hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors h-10 w-10 md:h-9 md:w-9"
				>
					<Bell className="!h-5 !w-5 md:!h-4 md:!w-4" />
					{unreadCount > 0 && (
						<Badge className="absolute -top-1 -right-1 h-4 min-w-4 sm:h-5 sm:min-w-5 flex items-center justify-center bg-red-600 hover:bg-red-600 text-white text-xs leading-none text-center rounded-full p-0 border-0">
							{unreadCount}
						</Badge>
					)}
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent
				align="end"
				className="w-80 max-h-[500px] overflow-hidden bg-white dark:bg-gray-800 "
			>
				<div className="flex items-center justify-between p-2">
					<DropdownMenuLabel className="text-base">
						Notifications
					</DropdownMenuLabel>
					<div className="flex items-center gap-2">
						<Button
							variant="ghost"
							size="icon"
							className="h-7 w-7"
							onClick={() => refreshNotifications()}
						>
							<RefreshCw
								className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
							/>
						</Button>
						{unreadCount > 0 && (
							<Button
								variant="ghost"
								size="sm"
								className="h-7 text-xs hover:bg-accent/20"
								onClick={handleMarkAllRead}
							>
								Mark all as read
							</Button>
						)}
					</div>
				</div>
				<DropdownMenuSeparator />

				<Tabs
					defaultValue="all"
					value={activeFilter}
					onValueChange={handleTabChange}
				>
					<TabsList className="grid grid-cols-4 w-full">
						<TabsTrigger value="all">All</TabsTrigger>
						<TabsTrigger value="news">News</TabsTrigger>
						<TabsTrigger value="update">Updates</TabsTrigger>
						<TabsTrigger value="personal">Personal</TabsTrigger>
					</TabsList>

					<TabsContent
						value={activeFilter}
						className="max-h-[350px] overflow-y-auto"
					>
						{isLoading ? (
							<div className="py-6 text-center">
								<Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
								<p className="text-sm text-muted-foreground">
									Loading notifications...
								</p>
							</div>
						) : displayNotifications.length > 0 ? (
							<div className="space-y-1">
								{displayNotifications.map((notification) => (
									<NotificationItem
										key={notification.id}
										notification={notification}
										// onClick={() => setOpen(false)}
									/>
								))}
							</div>
						) : (
							<div className="py-6 text-center text-sm text-muted-foreground">
								No notifications
							</div>
						)}
					</TabsContent>
				</Tabs>
			</DropdownMenuContent>
		</DropdownMenu>
	);
};
