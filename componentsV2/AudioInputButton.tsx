"use client";

import React, { useState, useCallback, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MicrophoneIcon } from "@/componentsV2/icons";
import { toast } from "sonner";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";

function AudioInputButton({
	setInput,
	isLoading,
	textareaRef,
}: {
	setInput: (value: string) => void;
	isLoading: boolean;
	textareaRef: React.RefObject<HTMLTextAreaElement>;
}) {
	const [isListening, setIsListening] = useState(false);
	const [permissionState, setPermissionState] =
		useState<PermissionState | null>(null);

	const mediaRecorderRef = useRef<MediaRecorder | null>(null);
	const streamRef = useRef<MediaStream | null>(null);
	const audioChunksRef = useRef<Blob[]>([]);

	// Check microphone permission
	useEffect(() => {
		navigator.permissions
			?.query({ name: "microphone" as PermissionName })
			.then((status) => {
				setPermissionState(status.state);
				status.onchange = () => setPermissionState(status.state);
			})
			.catch(() => setPermissionState(null));
	}, []);

	// Request stream from default device
	const requestStream = async (): Promise<MediaStream | null> => {
		try {
			const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
			streamRef.current = stream;
			return stream;
		} catch (err) {
			console.error("Microphone access error:", err);
			toast.error("Microphone access denied");
			return null;
		}
	};

	const startRecording = useCallback(async () => {
		const stream = await requestStream();
		if (!stream) return;

		const recorder = new MediaRecorder(stream);
		mediaRecorderRef.current = recorder;
		audioChunksRef.current = [];

		recorder.ondataavailable = (e) => {
			if (e.data.size > 0) audioChunksRef.current.push(e.data);
		};

		recorder.onstop = async () => {
			const audioBlob = new Blob(audioChunksRef.current, {
				type: "audio/webm",
			});
			const formData = new FormData();
			formData.append("file", audioBlob, "recording.webm");

			const toastId = toast.loading("Transcribing...");

			try {
				const res = await fetch("/api/transcribe", {
					method: "POST",
					body: formData,
				});

				if (!res.ok) throw new Error("Transcription failed");
				const { text } = await res.json();

				// Get current textarea value
				const currentText = textareaRef.current?.value || "";
				// Combine existing text with transcribed text, adding a space between
				const newText = currentText + (currentText ? " " : "") + text;

				setInput(newText);

				if (textareaRef.current) {
					textareaRef.current.value = newText;
					textareaRef.current.style.height = "auto";
					textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;
				}

				toast.success("Transcription complete", { id: toastId });
			} catch (err) {
				console.error("Transcription error:", err);
				toast.error("Transcription failed", { id: toastId });
			}

			streamRef.current?.getTracks().forEach((t) => t.stop());
			streamRef.current = null;
			setIsListening(false);
		};

		recorder.start();
		setIsListening(true);
		toast.success("Recording...");
	}, [setInput, textareaRef]);

	const stopRecording = useCallback(() => {
		mediaRecorderRef.current?.stop();
		mediaRecorderRef.current = null;
	}, []);

	const toggleRecording = useCallback(
		(e: React.MouseEvent) => {
			e.preventDefault();

			if (isListening) {
				stopRecording();
			} else {
				startRecording();
			}
		},
		[isListening, startRecording, stopRecording],
	);

	return (
		<Tooltip>
			<TooltipTrigger asChild>
				<Button
					className={`rounded-md p-2 h-8 dark:border-zinc-700 hover:dark:bg-zinc-900 hover:bg-zinc-200 flex items-center gap-1.5
            ${isListening ? "bg-red-500 hover:bg-red-600 text-white" : ""}
            ${permissionState === "denied" ? "bg-gray-400 hover:bg-gray-500 text-white" : ""}
          `}
					onClick={toggleRecording}
					disabled={isLoading || permissionState === "denied"}
					variant="ghost"
					type="button"
				>
					<MicrophoneIcon size={14} />
					<span className="hidden md:inline text-xs whitespace-nowrap">
						Dictate
					</span>
				</Button>
			</TooltipTrigger>
			<TooltipContent>
				{permissionState === "denied"
					? "Microphone access denied"
					: isListening
						? "Stop recording"
						: "Speak to Iqidis"}
			</TooltipContent>
		</Tooltip>
	);
}

export default React.memo(AudioInputButton);
