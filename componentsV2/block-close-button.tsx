import { memo } from "react";
import { CrossIcon } from "@/componentsV2/icons";
import { Button } from "@/components/ui/button";
import { initialBlockData, useBlock } from "@/hooks/use-block";

function PureBlockCloseButton() {
	const { setBlock } = useBlock();

	return (
		<Button
			variant="outline"
			className="h-fit p-2 dark:hover:bg-zinc-700"
			onClick={() => {
				setBlock((currentBlock: any) =>
					currentBlock.status === "streaming"
						? {
								...currentBlock,
								isVisible: false,
							}
						: { ...initialBlockData, status: "idle" },
				);
			}}
		>
			<CrossIcon size={18} />
		</Button>
	);
}

export const BlockCloseButton = memo(PureBlockCloseButton, () => true);
