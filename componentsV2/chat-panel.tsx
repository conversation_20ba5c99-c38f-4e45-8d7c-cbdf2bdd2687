"use client";

import type { Attachment } from "ai";
import { Expand, Shrink } from "lucide-react";
import type { Dispatch, SetStateAction } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import type { Vote } from "@/lib/db/schema";
import type { ExtendedMessage } from "@/lib/types";
import { Messages } from "@/componentsV2/messages";
import { MultimodalInput } from "@/componentsV2/multimodal-input";

export interface ChatPanelProps {
	chatId: string;
	isLoading: boolean;
	votes?: Array<Vote>;
	messages: ExtendedMessage[];
	setMessages: (
		messages:
			| ExtendedMessage[]
			| ((prev: ExtendedMessage[]) => ExtendedMessage[]),
	) => void;
	reload: () => Promise<string | null | undefined>;
	isReadonly: boolean;
	isBlockVisible: boolean;
	onRetry: (userMessageId?: string, useLegacyModel?: boolean) => void;
	scrollToBottomRef: React.MutableRefObject<
		((forceImmediate?: boolean) => void) | null
	>;
	isProcessing: boolean;
	hideThinking: boolean;
	query: string;
	onEdit: (newContent: string, useLegacyModel?: boolean) => void;
	input: string;
	setInput: (input: string) => void;
	handleSubmit: any;
	stop: () => void;
	attachments: Array<Attachment>;
	setAttachments: Dispatch<SetStateAction<Attachment[]>>;
	append: any;
	setSubmitFormRef: (submitFn: (useLegacyModel?: boolean) => void) => void;
	selectedModelId: string;
	chatTitle?: string;
	isFullscreen?: boolean;
	onToggleFullscreen?: () => void;
}

export function ChatPanel({
	chatId,
	isLoading,
	votes,
	messages,
	setMessages,
	reload,
	isReadonly,
	isBlockVisible,
	onRetry,
	scrollToBottomRef,
	isProcessing,
	hideThinking,
	query,
	onEdit,
	input,
	setInput,
	handleSubmit,
	stop,
	attachments,
	setAttachments,
	append,
	setSubmitFormRef,
	selectedModelId,
	chatTitle,
}: ChatPanelProps) {
	return (
		<div
			className={
				"flex-1 w-full xl:w-3/4 flex flex-col relative overflow-hidden custom-scrollbar h-full"
			}
		>
			<Messages
				chatId={chatId}
				isLoading={isLoading}
				votes={votes}
				messages={messages}
				setMessages={setMessages}
				reload={reload}
				isReadonly={isReadonly}
				isBlockVisible={isBlockVisible}
				onRetry={onRetry}
				scrollToBottomRef={scrollToBottomRef}
				isProcessing={isProcessing}
				hideThinking={hideThinking}
				query={query}
				onEdit={onEdit}
			/>
			<form
				onSubmit={(e) => {
					e.preventDefault();
					(handleSubmit as any)();
				}}
			>
				{!isReadonly && (
					<MultimodalInput
						chatId={chatId}
						input={input}
						setInput={setInput}
						handleSubmit={handleSubmit as any}
						isLoading={isLoading}
						stop={stop}
						attachments={attachments}
						setAttachments={setAttachments}
						messages={messages as any}
						setMessages={setMessages as any}
						append={append}
						setSubmitFormRef={setSubmitFormRef}
						selectedModelId={selectedModelId}
						chatTitle={chatTitle}
					/>
				)}
			</form>
		</div>
	);
}
