"use client";
import { PlusIcon, Search } from "lucide-react";
import type { User } from "next-auth";
import React, { useState, useRef, useCallback, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { SidebarHistory } from "@/componentsV2/sidebar-history";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

export function ChatSidebar({ user }: { user: User | undefined }) {
	const router = useRouter();
	const [searchQuery, setSearchQuery] = useState("");
	const [sidebarWidth, setSidebarWidth] = useState(300);
	const [isResizing, setIsResizing] = useState(false);
	const sidebarRef = useRef<HTMLDivElement>(null);

	const minWidth = 250;
	const maxWidth = 500;

	const handleMouseDown = useCallback((e: React.MouseEvent) => {
		setIsResizing(true);
		e.preventDefault();
	}, []);

	const handleMouseMove = useCallback(
		(e: MouseEvent) => {
			if (!isResizing || !sidebarRef.current) return;

			const rect = sidebarRef.current.getBoundingClientRect();
			const newWidth = e.clientX - rect.left;

			if (newWidth >= minWidth && newWidth <= maxWidth) {
				setSidebarWidth(newWidth);
			}
		},
		[isResizing, minWidth, maxWidth],
	);

	const handleMouseUp = useCallback(() => {
		setIsResizing(false);
	}, []);

	// Add global mouse event listeners
	useEffect(() => {
		if (isResizing) {
			document.addEventListener("mousemove", handleMouseMove);
			document.addEventListener("mouseup", handleMouseUp);
			document.body.style.cursor = "col-resize";
			document.body.style.userSelect = "none";
		} else {
			document.removeEventListener("mousemove", handleMouseMove);
			document.removeEventListener("mouseup", handleMouseUp);
			document.body.style.cursor = "";
			document.body.style.userSelect = "";
		}

		return () => {
			document.removeEventListener("mousemove", handleMouseMove);
			document.removeEventListener("mouseup", handleMouseUp);
			document.body.style.cursor = "";
			document.body.style.userSelect = "";
		};
	}, [isResizing, handleMouseMove, handleMouseUp]);

	return (
		<div
			ref={sidebarRef}
			className="flex flex-col overflow-hidden h-full border-r border-gray-200 dark:border-slate-700 relative"
			style={{ width: `${sidebarWidth}px` }}
		>
			<div className="p-4 shrink-0">
				{/* Header */}
				<div className="mb-4">
					<Button
						type="button"
						className="w-full shadow-md transition-all duration-200 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
						disabled={false}
						onClick={() => router.push("/")}
					>
						<div className="flex items-center gap-2">
							<PlusIcon className="size-4" />
							<span>New Chat</span>
						</div>
					</Button>
				</div>

				{/* Search Input */}
				<div className="relative">
					<Search className="absolute left-3 top-1/2 -translate-y-1/2 size-4 text-gray-400" />
					<Input
						placeholder="Search conversations..."
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						className="pl-10 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700"
					/>
				</div>
			</div>

			{/* Conversations Content */}
			<div className="flex-1 min-h-0 relative overflow-y-auto custom-scrollbar">
				<div className="px-4">
					<SidebarHistory user={user} searchQuery={searchQuery} />
				</div>
			</div>

			{/* Resize Handle */}
			<div
				className={`absolute top-0 right-0 w-[2px] h-full cursor-col-resize transition-colors duration-150 group ${
					isResizing
						? "bg-gray-700 dark:bg-blue-800"
						: "hover:bg-gray-700 dark:hover:bg-blue-800"
				}`}
				onMouseDown={handleMouseDown}
			>
				<div className="w-full h-full group-hover:w-2 transition-all duration-150" />
			</div>
		</div>
	);
}
