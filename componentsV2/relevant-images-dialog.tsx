import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>T<PERSON>le,
	DialogClose,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { CrossIcon } from "@/componentsV2/icons";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
	ZoomInIcon,
	ZoomOutIcon,
	XIcon,
	ChevronLeftIcon,
	ChevronRightIcon,
} from "lucide-react";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import React from "react";
import { LoaderIcon } from "@/componentsV2/icons";

interface RelevantImagesDialogProps {
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
	images?: Array<{ mime_type: string; url: string }>;
}

interface FullScreenImageProps {
	image: { mime_type: string; url: string };
	onClose: () => void;
	onNext: () => void;
	onPrev: () => void;
	hasNext: boolean;
	hasPrev: boolean;
	currentIndex: number;
	totalImages: number;
}

interface ImageLoadingState {
	[key: number]: boolean;
}

function FullScreenImage({
	image,
	onClose,
	onNext,
	onPrev,
	hasNext,
	hasPrev,
	currentIndex,
	totalImages,
}: FullScreenImageProps) {
	const [scale, setScale] = useState(1);
	const [isZoomed, setIsZoomed] = useState(false);
	const [isLoaded, setIsLoaded] = useState(false);

	const handleZoomIn = () => {
		setScale((prev) => Math.min(prev + 0.5, 3));
		setIsZoomed(true);
	};

	const handleZoomOut = () => {
		const newScale = Math.max(scale - 0.5, 1);
		setScale(newScale);
		setIsZoomed(newScale > 1);
	};

	const handleImageClick = (e: React.MouseEvent) => {
		e.stopPropagation();
		if (!isZoomed) {
			handleZoomIn();
		} else {
			setScale(1);
			setIsZoomed(false);
		}
	};

	return (
		<motion.div
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			exit={{ opacity: 0 }}
			className="fixed inset-0 z-[100] bg-black bg-opacity-90 flex items-center justify-center"
			onClick={onClose}
		>
			{/* Top controls */}
			<div className="absolute top-4 right-4 flex items-center gap-2 z-[101]">
				{/* Page indicator */}
				<div className="bg-black/50 px-3 py-1.5 rounded-md text-white text-sm">
					{currentIndex + 1}/{totalImages}
				</div>

				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="ghost"
							size="icon"
							className="bg-black/50 hover:bg-black/70 text-white"
							onClick={(e) => {
								e.stopPropagation();
								handleZoomIn();
							}}
						>
							<ZoomInIcon size={20} />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Zoom in</TooltipContent>
				</Tooltip>

				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="ghost"
							size="icon"
							className="bg-black/50 hover:bg-black/70 text-white"
							onClick={(e) => {
								e.stopPropagation();
								handleZoomOut();
							}}
							disabled={scale <= 1}
						>
							<ZoomOutIcon size={20} />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Zoom out</TooltipContent>
				</Tooltip>

				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="ghost"
							size="icon"
							className="bg-black/50 hover:bg-black/70 text-white"
							onClick={onClose}
						>
							<XIcon size={20} />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Close</TooltipContent>
				</Tooltip>
			</div>

			{/* Navigation buttons */}
			<div className="absolute left-4 top-1/2 -translate-y-1/2 z-[101]">
				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="ghost"
							size="icon"
							className="bg-black/50 hover:bg-black/70 text-white"
							onClick={(e) => {
								e.stopPropagation();
								onPrev();
							}}
							disabled={!hasPrev}
						>
							<ChevronLeftIcon size={24} />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Previous image</TooltipContent>
				</Tooltip>
			</div>

			<div className="absolute right-4 top-1/2 -translate-y-1/2 z-[101]">
				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant="ghost"
							size="icon"
							className="bg-black/50 hover:bg-black/70 text-white"
							onClick={(e) => {
								e.stopPropagation();
								onNext();
							}}
							disabled={!hasNext}
						>
							<ChevronRightIcon size={24} />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Next image</TooltipContent>
				</Tooltip>
			</div>

			<Tooltip>
				<TooltipTrigger asChild>
					<motion.div
						className="relative max-w-[90vw] max-h-[90vh] z-[101]"
						animate={{ scale }}
						transition={{ type: "spring", stiffness: 300, damping: 30 }}
						onClick={handleImageClick}
						style={{ cursor: isZoomed ? "zoom-out" : "zoom-in" }}
					>
						{!isLoaded && (
							<div className="absolute inset-0 flex items-center justify-center">
								<div className="animate-spin text-white">
									<LoaderIcon size={32} />
								</div>
							</div>
						)}
						<img
							src={image.url}
							alt="Full screen view"
							className={`max-w-full max-h-[90vh] object-contain ${!isLoaded ? "opacity-0" : "opacity-100"}`}
							onLoad={() => setIsLoaded(true)}
						/>
					</motion.div>
				</TooltipTrigger>
				<TooltipContent>
					{isZoomed ? "Click to zoom out" : "Click to zoom in"}
				</TooltipContent>
			</Tooltip>
		</motion.div>
	);
}

export function RelevantImagesDialog({
	isOpen,
	onOpenChange,
	images = [],
}: RelevantImagesDialogProps) {
	const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(
		null,
	);
	const [loadedImages, setLoadedImages] = useState<ImageLoadingState>({});

	useEffect(() => {
		if (!isOpen) {
			setLoadedImages({});
		}
	}, [isOpen]);

	const handleImageLoad = (index: number) => {
		setLoadedImages((prev) => ({ ...prev, [index]: true }));
	};

	const handleNext = () => {
		if (selectedImageIndex !== null && selectedImageIndex < images.length - 1) {
			setSelectedImageIndex(selectedImageIndex + 1);
		}
	};

	const handlePrev = () => {
		if (selectedImageIndex !== null && selectedImageIndex > 0) {
			setSelectedImageIndex(selectedImageIndex - 1);
		}
	};

	// Handle keyboard navigation
	const handleKeyDown = (event: KeyboardEvent) => {
		if (selectedImageIndex === null) return;

		switch (event.key) {
			case "ArrowRight":
				handleNext();
				break;
			case "ArrowLeft":
				handlePrev();
				break;
			case "Escape":
				setSelectedImageIndex(null);
				break;
		}
	};

	// Add keyboard event listener
	React.useEffect(() => {
		window.addEventListener("keydown", handleKeyDown);
		return () => window.removeEventListener("keydown", handleKeyDown);
	}, [selectedImageIndex]);

	return (
		<>
			<Dialog open={isOpen} onOpenChange={onOpenChange}>
				<DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
					<DialogHeader className="flex flex-row items-center justify-between">
						<DialogTitle>Relevant Images</DialogTitle>
						<DialogClose asChild>
							<Button variant="ghost" className="h-fit p-2">
								<CrossIcon size={18} />
							</Button>
						</DialogClose>
					</DialogHeader>
					{images.length > 0 ? (
						<div className="grid grid-cols-2 gap-4">
							{images.map((image, index) => (
								<Tooltip key={index}>
									<TooltipTrigger asChild>
										<div
											className="relative cursor-pointer transition-transform hover:scale-[1.02]"
											onClick={() => setSelectedImageIndex(index)}
										>
											{!loadedImages[index] && (
												<div className="absolute inset-0 flex items-center justify-center bg-muted rounded-lg">
													<div className="animate-spin text-muted-foreground">
														<LoaderIcon />
													</div>
												</div>
											)}
											<img
												src={image.url}
												alt={`Relevant image ${index + 1}`}
												className={`w-full h-auto rounded-lg ${!loadedImages[index] ? "opacity-0" : "opacity-100"}`}
												onLoad={() => handleImageLoad(index)}
											/>
										</div>
									</TooltipTrigger>
									<TooltipContent>Click to view fullscreen</TooltipContent>
								</Tooltip>
							))}
						</div>
					) : (
						<p>No relevant images available</p>
					)}
				</DialogContent>
			</Dialog>

			<AnimatePresence>
				{selectedImageIndex !== null && (
					<FullScreenImage
						image={images[selectedImageIndex]}
						onClose={() => setSelectedImageIndex(null)}
						onNext={handleNext}
						onPrev={handlePrev}
						hasNext={selectedImageIndex < images.length - 1}
						hasPrev={selectedImageIndex > 0}
						currentIndex={selectedImageIndex}
						totalImages={images.length}
					/>
				)}
			</AnimatePresence>
		</>
	);
}
