"use client";

import {
	MessageSquare,
	Folder<PERSON><PERSON>,
	ArrowRight,
	ChevronLeft,
	ChevronRight,
} from "lucide-react";
import {
	Sidebar,
	SidebarContent,
	SidebarGroup,
	SidebarGroupContent,
	SidebarGroupLabel,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	useSidebar,
	SidebarHeader,
	SidebarFooter,
} from "@/components/ui/sidebar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import type { User } from "@/lib/db/schema";
import Link from "next/link";
import { useParams, usePathname, useRouter } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import useSWR from "swr";
import { fetcher } from "@/lib/utils";
import { LogoIqidis } from "@/componentsV2/icons";
import { UserMenu } from "@/componentsV2/user-menu";
import { useTheme } from "next-themes";
import { useState, useEffect } from "react";

// Sample navigation items
const mainNavItems = [
	{
		title: "AI Assistant",
		icon: MessageSquare,
		href: "/",
	},
	{
		title: "Library",
		icon: FolderOpen,
		href: "/library",
	},
];

interface Chat {
	id: string;
	title: string;
	createdAt: string;
	updatedAt: string;
}

interface Document {
	id: string;
	originalName: string;
	createdAt: string;
	usedCount: number;
	size: number;
	folderName?: string;
}

export function AppSidebar({ user }: { user?: User }) {
	const { state, toggleSidebar } = useSidebar();
	const { id } = useParams();
	const pathname = usePathname();
	const router = useRouter();
	const { resolvedTheme } = useTheme();

	// State for pagination
	const [chatDisplayCount, setChatDisplayCount] = useState(5);
	const [documentDisplayCount, setDocumentDisplayCount] = useState(5);

	// Check if screen is mobile (less than md breakpoint)
	const [isMobile, setIsMobile] = useState(false);

	// Effect to handle responsive behavior
	useEffect(() => {
		const checkScreenSize = () => {
			setIsMobile(window.innerWidth < 768);
		};

		checkScreenSize();
		window.addEventListener("resize", checkScreenSize);

		return () => window.removeEventListener("resize", checkScreenSize);
	}, []);

	// Fetch recent chats
	const { data: recentChats, isLoading: isLoadingChats } = useSWR<Chat[]>(
		user ? "/api/history" : null,
		fetcher,
		{
			fallbackData: [],
			revalidateOnFocus: false,
		},
	);

	// Fetch recent documents
	const { data: recentDocuments, isLoading: isLoadingDocuments } = useSWR<{
		documents: Document[];
	}>(user ? "/api/documents/recently?limit=50" : null, fetcher, {
		fallbackData: { documents: [] },
		revalidateOnFocus: false,
	});

	// Get displayed items based on pagination
	const displayedChats = recentChats?.slice(0, chatDisplayCount) || [];
	const displayedDocuments =
		recentDocuments?.documents?.slice(0, documentDisplayCount) || [];

	// Check if there are more items to show
	const hasMoreChats = recentChats && recentChats.length > chatDisplayCount;
	const hasMoreDocuments =
		recentDocuments?.documents &&
		recentDocuments.documents.length > documentDisplayCount;

	// Handle route navigation for UserMenu
	const handleRoute = (path: string) => {
		router.push(path);
	};

	// Simplified function to check if a menu item is active
	const isMenuItemActive = (itemHref: string) => {
		if (itemHref === "/") {
			return pathname === "/" || pathname.startsWith("/chat");
		}
		return pathname.startsWith(itemHref);
	};

	return (
		<Sidebar
			collapsible={isMobile ? "offcanvas" : "icon"}
			variant="sidebar"
			className={cn(
				"chat-background-light dark:chat-background-dark",
				isMobile && "md:w-64",
			)}
		>
			{/* Sidebar Header with Logo */}
			<SidebarHeader className="py-3 px-3 flex justify-center">
				<Link href="/" className="flex items-center gap-3">
					<div
						className={cn(
							"flex items-center mt-[2px]",
							state === "expanded" || isMobile
								? "gap-2"
								: "justify-center w-full",
						)}
					>
						<LogoIqidis
							size={50}
							mixBlendMode={resolvedTheme === "dark" ? "lighten" : "multiply"}
							isDark={resolvedTheme === "dark"}
						/>
						{(state === "expanded" || isMobile) && (
							<div className="flex flex-col">
								<span className="text-xl font-bold">Iqidis</span>
							</div>
						)}
					</div>
				</Link>
			</SidebarHeader>

			<SidebarContent className="px-3 py-3">
				{/* Main Navigation - Always visible */}
				<SidebarGroup>
					<SidebarGroupContent>
						<SidebarMenu>
							{/* Main navigation items */}
							{mainNavItems.map((item) => (
								<SidebarMenuItem key={item.href}>
									<SidebarMenuButton
										asChild
										tooltip={state === "collapsed" ? item.title : undefined}
										className={cn(
											"h-10 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-slate-800 dark:hover:text-white",
											state === "collapsed" && !isMobile
												? "justify-center"
												: "justify-start",
											isMenuItemActive(item.href)
												? "bg-gray-100 dark:bg-slate-800"
												: "",
										)}
									>
										<Link
											href={item.href}
											className={cn(
												"flex items-center p-4 text-base w-full",
												state === "expanded" || isMobile
													? "gap-3"
													: "justify-center",
											)}
										>
											<item.icon
												className="size-5 shrink-0"
												style={{ height: "18px", width: "18px" }}
											/>
											{(state === "expanded" || isMobile) && (
												<span className="text-sm">{item.title}</span>
											)}
										</Link>
									</SidebarMenuButton>
								</SidebarMenuItem>
							))}
						</SidebarMenu>
					</SidebarGroupContent>
				</SidebarGroup>

				{/* Recent Chats - Only visible when expanded or on mobile */}
				{(state === "expanded" || isMobile) && (
					<SidebarGroup>
						<SidebarGroupLabel className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
							Recent Chats
						</SidebarGroupLabel>
						<SidebarGroupContent>
							<ScrollArea className="h-[300px]">
								<SidebarMenu>
									{isLoadingChats ? (
										// Loading skeletons for chats
										Array.from({ length: 5 }).map((_, index) => (
											<SidebarMenuItem key={`chat-skeleton-${index}`}>
												<div className="px-2 py-2">
													<Skeleton className="h-4 w-full" />
												</div>
											</SidebarMenuItem>
										))
									) : (
										<>
											{displayedChats.map((chat) => (
												<SidebarMenuItem key={chat.id}>
													<SidebarMenuButton
														asChild
														className={cn(
															"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-slate-800 dark:hover:text-white",
															chat.id === id
																? "bg-gray-100 dark:bg-slate-800"
																: "",
														)}
													>
														<Link
															href={`/chat/${chat.id}`}
															className="flex items-center"
														>
															<span className="truncate text-sm font-medium max-w-[250px]">
																{chat.title}
															</span>
														</Link>
													</SidebarMenuButton>
												</SidebarMenuItem>
											))}
											{displayedChats.length === 0 && (
												<div className="px-2 py-1 text-xs text-gray-500 dark:text-gray-400">
													No recent chats
												</div>
											)}
											{hasMoreChats && (
												<SidebarMenuItem>
													<SidebarMenuButton
														onClick={() =>
															setChatDisplayCount((prev) => prev + 5)
														}
														className="hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-slate-800 dark:hover:text-white"
													>
														<div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
															<ArrowRight className="h-3 w-3" />
															<span className="text-xs">See more</span>
														</div>
													</SidebarMenuButton>
												</SidebarMenuItem>
											)}
										</>
									)}
								</SidebarMenu>
							</ScrollArea>
						</SidebarGroupContent>
					</SidebarGroup>
				)}

				{/* Recent Files - Only visible when expanded or on mobile */}
				{(state === "expanded" || isMobile) && (
					<SidebarGroup>
						<SidebarGroupLabel className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
							Recent Files
						</SidebarGroupLabel>
						<SidebarGroupContent>
							<ScrollArea className="h-[300px]">
								<SidebarMenu>
									{isLoadingDocuments ? (
										// Loading skeletons for documents
										Array.from({ length: 5 }).map((_, index) => (
											<SidebarMenuItem key={`doc-skeleton-${index}`}>
												<div className="px-2 py-2">
													<Skeleton className="h-4 w-full" />
												</div>
											</SidebarMenuItem>
										))
									) : (
										<>
											{displayedDocuments.map((doc) => (
												<SidebarMenuItem key={doc.id}>
													<SidebarMenuButton
														asChild
														className="hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-slate-800 dark:hover:text-white"
													>
														<Link
															href={`/library`}
															className="flex items-center"
														>
															<span className="truncate text-sm font-medium max-w-[250px]">
																{doc.originalName}
															</span>
														</Link>
													</SidebarMenuButton>
												</SidebarMenuItem>
											))}
											{displayedDocuments.length === 0 && (
												<div className="px-2 py-1 text-xs text-gray-500 dark:text-gray-400">
													No recent files
												</div>
											)}
											{hasMoreDocuments && (
												<SidebarMenuItem>
													<SidebarMenuButton
														onClick={() =>
															setDocumentDisplayCount((prev) => prev + 5)
														}
														className="hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-slate-800 dark:hover:text-white"
													>
														<div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
															<ArrowRight className="h-3 w-3" />
															<span className="text-xs">See more</span>
														</div>
													</SidebarMenuButton>
												</SidebarMenuItem>
											)}
										</>
									)}
								</SidebarMenu>
							</ScrollArea>
						</SidebarGroupContent>
					</SidebarGroup>
				)}
			</SidebarContent>

			{/* Sidebar Footer with UserMenu */}
			<SidebarFooter className="px-3 py-3">
				<div
					className={cn(
						"flex items-center",
						state === "expanded" || isMobile
							? "justify-between"
							: "justify-center",
					)}
				>
					{(state === "expanded" || isMobile) && user && (
						<div className="flex items-center gap-2 flex-1 min-w-0">
							<span className="text-sm truncate">{user.email}</span>
						</div>
					)}
					<UserMenu handleRoute={handleRoute} />
				</div>
			</SidebarFooter>

			{/* Floating Sidebar Toggle Button - Hidden on mobile */}
			{!isMobile && (
				<Button
					variant="ghost"
					size="icon"
					onClick={toggleSidebar}
					className="absolute bg-transparent hover:bg-transparent backdrop-blur-sm border border-border/50 rounded-full shadow-sm z-10 transition-all duration-200 hover:scale-150 hover:shadow-md"
					style={{
						top: "84px",
						right: "0",
						transform: "translateX(50%)",
					}}
				>
					{state === "collapsed" ? (
						<ChevronRight className="!size-6 text-gray-500 dark:text-gray-400 transition-colors duration-200" />
					) : (
						<ChevronLeft className="!size-6 text-gray-500 dark:text-gray-400 transition-colors duration-200" />
					)}
				</Button>
			)}
		</Sidebar>
	);
}
