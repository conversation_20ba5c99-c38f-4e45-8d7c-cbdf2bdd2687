"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { Upload, Save, FileText, Loader, X } from "lucide-react";
import { toast } from "sonner";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
	UserIcon,
	BookOpenIcon,
	PenToolIcon,
	FileTextIcon,
	SparklesIcon,
} from "lucide-react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { ChatHeader } from "@/components/chat-header";
import { logEvent } from "@/lib/analytics/events-client";
import { PreferencesEvent } from "@/lib/analytics/event-types";
import { useUser } from "@/contexts/UserContext";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";

const preferencesFormSchema = z.object({
	fullName: z.string().optional(),
	roleTitle: z.string().optional(),
	firmName: z.string().optional(),
	barId: z.string().optional(),
	email: z.union([z.string().email(), z.string().length(0)]).optional(),
	officePhone: z.string().optional(),
	directPhone: z.string().optional(),
	firmAddress: z.string().optional(),
	practiceAreas: z.string().optional(), // Changed from required to optional
	jurisdictions: z.string().optional(), // Changed from required to optional
	formalityScale: z.number().min(0).max(100).optional(), // Added optional
	riskToleranceScale: z.number().min(0).max(100).optional(), // Added optional
	detailLevel: z.number().min(0).max(100).optional(), // Added optional
	preferredReferences: z.string().optional(), // Changed from required to optional
	documentFormatting: z.string().optional(), // Changed from required to optional
	miscInformation: z.string().optional(),
	aiGeneratedProfile: z.string().optional(),
});

export function PreferencesForm({
	initialData,
	isLoading,
	onSuccessfulSubmit,
}: {
	initialData?: any;
	isLoading?: boolean;
	onSuccessfulSubmit?: () => void;
}) {
	const router = useRouter();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isDirty, setIsDirty] = useState(false);
	const [isGeneratingProfile, setIsGeneratingProfile] = useState(false);
	const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
	const [activeTab, setActiveTab] = useState("professional");
	const [deletingFiles, setDeletingFiles] = useState<string[]>([]);
	const user = useUser();

	const handleBackClick = (e: React.MouseEvent) => {
		e.preventDefault();
		router.back();
	};

	const form = useForm({
		resolver: zodResolver(preferencesFormSchema),
		defaultValues: {
			fullName: "",
			roleTitle: "",
			firmName: "",
			barId: "",
			email: "",
			officePhone: "",
			directPhone: "",
			firmAddress: "",
			practiceAreas: "",
			jurisdictions: "",
			formalityScale: 50,
			riskToleranceScale: 50,
			detailLevel: 50,
			preferredReferences: "",
			documentFormatting: "",
			miscInformation: "",
			aiGeneratedProfile: "",
		},
		mode: "onChange",
	});

	// Reset form when initialData changes
	useEffect(() => {
		if (initialData) {
			// Merge initialData with default values to ensure no undefined values
			const mergedData = {
				fullName: initialData.fullName ?? "",
				roleTitle: initialData.roleTitle ?? "",
				firmName: initialData.firmName ?? "",
				barId: initialData.barId ?? "",
				email: initialData.email ?? "",
				officePhone: initialData.officePhone ?? "",
				directPhone: initialData.directPhone ?? "",
				firmAddress: initialData.firmAddress ?? "",
				practiceAreas: initialData.practiceAreas ?? "",
				jurisdictions: initialData.jurisdictions ?? "",
				formalityScale: initialData.formalityScale ?? 50,
				riskToleranceScale: initialData.riskToleranceScale ?? 50,
				detailLevel: initialData.detailLevel ?? 50,
				preferredReferences: initialData.preferredReferences ?? "",
				documentFormatting: initialData.documentFormatting ?? "",
				miscInformation: initialData.miscInformation ?? "",
				aiGeneratedProfile: initialData.aiGeneratedProfile ?? "",
			};
			form.reset(mergedData);
		}
	}, [initialData, form]);

	// Watch for form changes
	useEffect(() => {
		// Initially set isDirty to false
		setIsDirty(false);

		// Subscribe to form changes
		const subscription = form.watch(() => {
			// Only set isDirty to true if there are actual changes
			if (form.formState.isDirty) {
				setIsDirty(true);
			}
		});

		return () => subscription.unsubscribe();
	}, [form]);

	if (isLoading) {
		return (
			<div className="max-w-4xl mx-auto space-y-8">
				{[1, 2, 3].map((i) => (
					<Card key={i} className="shadow-sm">
						<CardHeader>
							<div className="h-6 w-1/4 bg-muted animate-pulse rounded" />
							<div className="h-4 w-2/4 bg-muted animate-pulse rounded" />
						</CardHeader>
						<CardContent className="space-y-4">
							{[1, 2, 3].map((j) => (
								<div key={j} className="space-y-2">
									<div className="h-4 w-1/4 bg-muted animate-pulse rounded" />
									<div className="h-10 w-full bg-muted animate-pulse rounded" />
								</div>
							))}
						</CardContent>
					</Card>
				))}
			</div>
		);
	}

	const onSubmit = async (data: any) => {
		// If no changes, don't submit
		if (!isDirty) {
			toast.info("No changes to save");
			return;
		}

		logEvent(PreferencesEvent.PROFESSIONAL_PREFERENCES, {
			userId: user?.id,
			userEmail: user?.email,
			isAdmin: user?.isAdmin,
			step: "clicked save preferences button",
			stepNumber: "2",
		});

		try {
			setIsSubmitting(true);

			// Ensure all required fields have at least empty string values
			const dataToSubmit = {
				...data,
				practiceAreas: data.practiceAreas || "",
				jurisdictions: data.jurisdictions || "",
				formalityScale: data.formalityScale ?? 50,
				riskToleranceScale: data.riskToleranceScale ?? 50,
				detailLevel: data.detailLevel ?? 50,
				preferredReferences: data.preferredReferences || "",
				documentFormatting: data.documentFormatting || "",
			};

			// console.log("Submitting preferences data:", dataToSubmit);

			const response = await fetch("/api/preferences", {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(dataToSubmit),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Failed to save preferences");
			}

			toast.success("Preferences saved successfully");
			setIsDirty(false);

			// After successful save, also update the user profile if needed
			if (data.fullName || data.firmName) {
				try {
					const nameParts = data.fullName ? data.fullName.split(" ") : [];
					const firstName = nameParts[0] || "";
					const lastName =
						nameParts.length > 1 ? nameParts.slice(1).join(" ") : "";

					await fetch("/api/profile/update-all", {
						method: "POST",
						headers: { "Content-Type": "application/json" },
						body: JSON.stringify({
							firstName,
							lastName,
							organization: data.firmName || "",
							email: data.email || "",
						}),
					});

					// Call the callback if provided
					if (onSuccessfulSubmit) {
						onSuccessfulSubmit();
					}
				} catch (profileError) {
					console.error("Error syncing with profile:", profileError);
					// Don't show error to user as preferences were saved successfully
				}
			} else if (onSuccessfulSubmit) {
				// Still call the callback even if no profile update was needed
				onSuccessfulSubmit();
			}
		} catch (error) {
			console.error("Error saving preferences:", error);
			toast.error(
				error instanceof Error ? error.message : "Failed to save preferences",
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const generateProfile = async (files: File[]) => {
		setIsGeneratingProfile(true);
		try {
			const formData = new FormData();
			files.forEach((file) => formData.append("documents", file));

			const response = await fetch("/api/preferences/generate-profile", {
				method: "POST",
				body: formData,
			});

			if (!response.ok) throw new Error("Failed to generate profile");

			const { profile } = await response.json();
			form.setValue("aiGeneratedProfile", profile);
		} catch (error) {
			console.error("Error generating profile:", error);
			// Add error handling UI here
		} finally {
			setIsGeneratingProfile(false);
			setUploadedFiles([]);
		}
	};

	const handleFileDelete = (fileIndex: number) => {
		const fileToRemove = uploadedFiles[fileIndex];
		setDeletingFiles((prev) => [...prev, fileToRemove.name]);

		// Create a new array without the deleted file
		const newFiles = [...uploadedFiles];
		newFiles.splice(fileIndex, 1);
		setUploadedFiles(newFiles);

		setDeletingFiles((prev) =>
			prev.filter((name) => name !== fileToRemove.name),
		);
	};

	const tabVariants = {
		hidden: { opacity: 0, x: 20 },
		visible: {
			opacity: 1,
			x: 0,
			transition: {
				duration: 0.3,
				ease: "easeInOut",
			},
		},
		exit: {
			opacity: 0,
			x: -20,
			transition: {
				duration: 0.2,
				ease: "easeInOut",
			},
		},
	};

	return (
		<Form {...form}>
			<div className="flex flex-col min-w-full h-full">
				{/* <ChatHeader
          chatId=""
          selectedModelId=""
          selectedVisibilityType="private"
          isReadonly={false}
          title="Professional Preferences"
        /> */}
				<form
					onSubmit={(e) => {
						e.preventDefault();

						// Get the current form values
						const formData = form.getValues();

						// Call onSubmit directly with the form data
						onSubmit(formData);
					}}
					className="mx-auto space-y-2 h-full w-full"
					noValidate
				>
					<Tabs
						value={activeTab}
						onValueChange={setActiveTab}
						className="w-full"
					>
						<div className="rounded p-2 border shadow-sm w-full overflow-auto lg:mt-0 mt-6">
							<TabsList className="w-full justify-start bg-transparent">
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<TabsTrigger
												value="professional"
												className={`
                              flex items-center gap-2 px-4 proActTabs
                              hover:bg-zinc-100 dark:hover:bg-zinc-800
                              data-[state=active]:font-medium
                              data-[state=active]:text-[rgb(var(--base-navy))]
                              dark:[aria-selected=true]:text-white
                            `}
												style={{
													backgroundColor:
														activeTab === "professional"
															? "rgb(var(--base-navy), 0.1)"
															: "",
													fontWeight: activeTab === "professional" ? "500" : "",
												}}
											>
												<UserIcon className="size-4" />
												<span>Professional Info</span>
											</TabsTrigger>
										</TooltipTrigger>
										<TooltipContent side="bottom">
											Your personal and contact information
										</TooltipContent>
									</Tooltip>

									<Tooltip>
										<TooltipTrigger asChild>
											<TabsTrigger
												value="practice"
												className="flex items-center gap-2 px-4 hover:bg-zinc-100 dark:hover:bg-zinc-800 data-[state=active]:text-[rgb(var(--base-navy))] data-[state=active]:font-medium proActTabs"
												style={{
													backgroundColor:
														activeTab === "practice"
															? "rgb(var(--base-navy), 0.1)"
															: "",
													fontWeight: activeTab === "practice" ? "500" : "",
												}}
											>
												<BookOpenIcon className="size-4" />
												<span>Practice Areas</span>
											</TabsTrigger>
										</TooltipTrigger>
										<TooltipContent side="bottom">
											Your legal specialties and jurisdictions
										</TooltipContent>
									</Tooltip>

									<Tooltip>
										<TooltipTrigger asChild>
											<TabsTrigger
												value="drafting"
												className="flex items-center gap-2 px-4 hover:bg-zinc-100 dark:hover:bg-zinc-800 data-[state=active]:text-[rgb(var(--base-navy))] data-[state=active]:font-medium proActTabs"
												style={{
													backgroundColor:
														activeTab === "drafting"
															? "rgb(var(--base-navy), 0.1)"
															: "",
													fontWeight: activeTab === "drafting" ? "500" : "",
												}}
											>
												<PenToolIcon className="size-4" />
												<span>Drafting Style</span>
											</TabsTrigger>
										</TooltipTrigger>
										<TooltipContent side="bottom">
											Customize your writing style preferences
										</TooltipContent>
									</Tooltip>

									<Tooltip>
										<TooltipTrigger asChild>
											<TabsTrigger
												value="ai"
												className="flex items-center gap-2 px-4 hover:bg-zinc-100 dark:hover:bg-zinc-800 data-[state=active]:text-[rgb(var(--base-navy))] data-[state=active]:font-medium proActTabs"
												style={{
													backgroundColor:
														activeTab === "ai"
															? "rgb(var(--base-navy), 0.1)"
															: "",
													fontWeight: activeTab === "ai" ? "500" : "",
												}}
											>
												<SparklesIcon className="size-4" />
												<span>AI Profile</span>
											</TabsTrigger>
										</TooltipTrigger>
										<TooltipContent side="bottom">
											Generate an AI profile from your documents
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							</TabsList>
						</div>

						<TabsContent value="professional" asChild>
							<motion.div
								initial="hidden"
								animate="visible"
								exit="exit"
								variants={tabVariants}
								className="space-y-4"
							>
								<Card className="shadow-sm">
									<CardHeader>
										<CardTitle className="text-[rgb(var(--base-navy))]">
											Professional Information
										</CardTitle>
										<CardDescription>
											Your key contact details and credentials
										</CardDescription>
									</CardHeader>
									<CardContent className="space-y-4">
										<div className="grid gap-4 md:grid-cols-2">
											<FormField
												control={form.control}
												name="fullName"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-[rgb(var(--base-navy))]">
															Full Name
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																className="bg-background focus-visible:ring-[rgb(var(--base-navy))]"
															/>
														</FormControl>
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name="roleTitle"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-[rgb(var(--base-navy))]">
															Role/Title
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																className="bg-background focus-visible:ring-[rgb(var(--base-navy))]"
															/>
														</FormControl>
													</FormItem>
												)}
											/>
										</div>
										<div className="grid gap-4 md:grid-cols-2">
											<FormField
												control={form.control}
												name="firmName"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-[rgb(var(--base-navy))]">
															Firm/Company Name
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																className="bg-background focus-visible:ring-[rgb(var(--base-navy))]"
															/>
														</FormControl>
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name="barId"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-[rgb(var(--base-navy))]">
															Bar ID
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																className="bg-background focus-visible:ring-[rgb(var(--base-navy))]"
															/>
														</FormControl>
													</FormItem>
												)}
											/>
										</div>
										<FormField
											control={form.control}
											name="email"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-[rgb(var(--base-navy))]">
														Email Address
													</FormLabel>
													<FormControl>
														<Input
															{...field}
															disabled={true}
															type="email"
															className="bg-muted focus-visible:ring-[rgb(var(--base-navy))]"
														/>
													</FormControl>
													<FormDescription className="sm:text-sm text-xs">
														Your email address cannot be changed
													</FormDescription>
												</FormItem>
											)}
										/>
										<div className="grid gap-4 md:grid-cols-2">
											<FormField
												control={form.control}
												name="officePhone"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-[rgb(var(--base-navy))]">
															Office Phone Number
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																className="bg-background focus-visible:ring-[rgb(var(--base-navy))]"
															/>
														</FormControl>
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name="directPhone"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-[rgb(var(--base-navy))]">
															Direct Phone Number
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																className="bg-background focus-visible:ring-[rgb(var(--base-navy))]"
															/>
														</FormControl>
													</FormItem>
												)}
											/>
										</div>
										<FormField
											control={form.control}
											name="firmAddress"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-[rgb(var(--base-navy))]">
														Office Address
													</FormLabel>
													<FormControl>
														<Textarea
															{...field}
															className="bg-background resize-none focus-visible:ring-[rgb(var(--base-navy))]"
														/>
													</FormControl>
												</FormItem>
											)}
										/>
									</CardContent>
								</Card>
							</motion.div>
						</TabsContent>

						<TabsContent value="practice" asChild>
							<motion.div
								initial="hidden"
								animate="visible"
								exit="exit"
								variants={tabVariants}
								className="space-y-4"
							>
								<Card className="shadow-sm">
									<CardHeader>
										<CardTitle className="text-[rgb(var(--base-navy))]">
											Practice Areas & Jurisdictions
										</CardTitle>
										<CardDescription>
											Define your areas of expertise and where you&apos;re
											admitted to practice
										</CardDescription>
									</CardHeader>
									<CardContent className="space-y-6 md:p-6 p-4">
										<FormField
											control={form.control}
											name="practiceAreas"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-[rgb(var(--base-navy))]">
														Practice Areas
													</FormLabel>
													<FormDescription className="mb-2 !sm:mt-2 !mt-0 text-[rgb(var(--base-navy))]/70">
														List your primary areas of legal practice
													</FormDescription>
													<FormControl>
														<Textarea
															{...field}
															className="sm:text-base text-sm bg-background resize-none min-h-[100px] focus-visible:ring-[rgb(var(--base-navy))]"
															placeholder="e.g., Corporate Law, Intellectual Property, Litigation"
														/>
													</FormControl>
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="jurisdictions"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-[rgb(var(--base-navy))]">
														Jurisdictions
													</FormLabel>
													<FormDescription className="mb-2 !sm:mt-2 !mt-0 text-[rgb(var(--base-navy))]/70">
														List the jurisdictions where you are licensed to
														practice
													</FormDescription>
													<FormControl>
														<Textarea
															{...field}
															className="sm:text-base text-sm bg-background resize-none min-h-[100px] focus-visible:ring-[rgb(var(--base-navy))]"
															placeholder="e.g., New York, California, Federal"
														/>
													</FormControl>
												</FormItem>
											)}
										/>
									</CardContent>
								</Card>
							</motion.div>
						</TabsContent>

						<TabsContent value="drafting" asChild>
							<motion.div
								initial="hidden"
								animate="visible"
								exit="exit"
								variants={tabVariants}
								className="space-y-4"
							>
								<Card className="shadow-sm">
									<CardHeader>
										<CardTitle className="text-[rgb(var(--base-navy))]">
											Drafting Style
										</CardTitle>
										<CardDescription>
											Adjust your drafting tone, risk tolerance, and detail
											level
										</CardDescription>
									</CardHeader>
									<CardContent>
										<div className="space-y-8">
											<FormField
												control={form.control}
												name="formalityScale"
												render={({ field }) => (
													<FormItem>
														<div className="flex justify-between items-center mb-2">
															<FormLabel className="text-[rgb(var(--base-navy))]">
																Formality Level
															</FormLabel>
															<span className="text-sm text-[rgb(var(--base-navy))]">
																{field.value}%
															</span>
														</div>
														<FormDescription className="mb-2 text-[rgb(var(--base-navy))]/70">
															Adjust how your documents read—from conversational
															to highly formal
														</FormDescription>
														<FormControl>
															<div className="space-y-1">
																<Slider
																	value={[field.value]}
																	onValueChange={([value]) =>
																		field.onChange(value)
																	}
																	min={0}
																	max={100}
																	step={1}
																	className="[&_[role=slider]]:h-4 [&_[role=slider]]:w-4 [&_[role=slider]]:bg-[rgb(var(--base-navy))] [&>[role=slider]]:focus:ring-[rgb(var(--base-navy))] [&_.range]:bg-[rgb(var(--base-navy))]"
																/>
																<div className="flex justify-between text-sm text-[rgb(var(--base-navy))]">
																	<span>Casual</span>
																	<span>Formal</span>
																</div>
															</div>
														</FormControl>
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="riskToleranceScale"
												render={({ field }) => (
													<FormItem>
														<div className="flex justify-between items-center mb-2">
															<FormLabel className="text-[rgb(var(--base-navy))]">
																Risk Tolerance
															</FormLabel>
															<span className="text-sm text-[rgb(var(--base-navy))]">
																{field.value}%
															</span>
														</div>
														<FormDescription className="mb-2 text-[rgb(var(--base-navy))]/70">
															Do you typically include extra protective clauses
															(Conservative) or prefer more concise,
															business-friendly language (Aggressive)?
														</FormDescription>
														<FormControl>
															<div className="space-y-1">
																<Slider
																	value={[field.value]}
																	onValueChange={([value]) =>
																		field.onChange(value)
																	}
																	min={0}
																	max={100}
																	step={1}
																	className="[&_[role=slider]]:h-4 [&_[role=slider]]:w-4 [&_[role=slider]]:bg-[rgb(var(--base-navy))] [&>[role=slider]]:focus:ring-[rgb(var(--base-navy))] [&_.range]:bg-[rgb(var(--base-navy))]"
																/>
																<div className="flex justify-between text-sm text-[rgb(var(--base-navy))]">
																	<span>Conservative</span>
																	<span>Aggressive</span>
																</div>
															</div>
														</FormControl>
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="detailLevel"
												render={({ field }) => (
													<FormItem>
														<div className="flex justify-between items-center mb-2">
															<FormLabel className="text-[rgb(var(--base-navy))]">
																Detail Level
															</FormLabel>
															<span className="text-sm text-[rgb(var(--base-navy))]">
																{field.value}%
															</span>
														</div>
														<FormDescription className="mb-2 text-[rgb(var(--base-navy))]/70">
															Basic might produce concise summarizations, while
															Detailed includes thorough disclaimers, footnotes,
															etc.
														</FormDescription>
														<FormControl>
															<div className="space-y-1">
																<Slider
																	value={[field.value]}
																	onValueChange={([value]) =>
																		field.onChange(value)
																	}
																	min={0}
																	max={100}
																	step={1}
																	className="[&_[role=slider]]:h-4 [&_[role=slider]]:w-4 [&_[role=slider]]:bg-[rgb(var(--base-navy))] [&>[role=slider]]:focus:ring-[rgb(var(--base-navy))] [&_.range]:bg-[rgb(var(--base-navy))]"
																/>
																<div className="flex justify-between text-sm text-[rgb(var(--base-navy))]">
																	<span>Basic</span>
																	<span>Detailed</span>
																</div>
															</div>
														</FormControl>
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="miscInformation"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-[rgb(var(--base-navy))]">
															Additional Drafting Instructions
														</FormLabel>
														<FormDescription className="mb-2 !sm:mt-2 !mt-0 text-[rgb(var(--base-navy))]/70">
															Any specific writing style preferences you&apos;d
															like to include
														</FormDescription>
														<FormControl>
															<Textarea
																{...field}
																className="bg-background resize-none min-h-[100px] focus-visible:ring-[rgb(var(--base-navy))]"
																placeholder="e.g., I prefer a casual, conversational tone with short, punchy sentences."
															/>
														</FormControl>
													</FormItem>
												)}
											/>
										</div>
									</CardContent>
								</Card>
							</motion.div>
						</TabsContent>

						<TabsContent value="document" asChild>
							<motion.div
								initial="hidden"
								animate="visible"
								exit="exit"
								variants={tabVariants}
								className="space-y-4"
							>
								<Card className="shadow-sm">
									<CardHeader>
										<CardTitle>Document Preferences</CardTitle>
									</CardHeader>
									<CardContent className="space-y-6">
										<FormField
											control={form.control}
											name="preferredReferences"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-[rgb(var(--base-navy))]">
														Preferred References
													</FormLabel>
													<FormControl>
														<Textarea
															{...field}
															className="bg-background resize-none min-h-[100px] focus-visible:ring-[rgb(var(--base-navy))]"
															placeholder="E.g., Cite more Federal cases, Focus on State-level authority"
														/>
													</FormControl>
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="documentFormatting"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-[rgb(var(--base-navy))]">
														Document Formatting Guide
													</FormLabel>
													<FormControl>
														<Textarea
															{...field}
															className="bg-background resize-none min-h-[100px] focus-visible:ring-[rgb(var(--base-navy))]"
															placeholder="Specify your preferred formatting guidelines"
														/>
													</FormControl>
												</FormItem>
											)}
										/>
									</CardContent>
								</Card>
							</motion.div>
						</TabsContent>

						<TabsContent value="ai" asChild>
							<motion.div
								initial="hidden"
								animate="visible"
								exit="exit"
								variants={tabVariants}
								className="space-y-4"
							>
								<Card className="shadow-sm">
									<CardHeader>
										<CardTitle className="text-[rgb(var(--base-navy))]">
											AI Profile Generation
										</CardTitle>
										<CardDescription>
											Upload work products to generate your AI profile
										</CardDescription>
									</CardHeader>
									<CardContent className="space-y-4">
										<div className="border-2 border-dashed rounded-lg p-8 transition-colors hover:border-primary/50 hover:bg-muted/50">
											<input
												type="file"
												multiple
												className="hidden"
												id="file-upload"
												onChange={(e) =>
													setUploadedFiles(Array.from(e.target.files || []))
												}
												accept=".pdf,.doc,.docx"
											/>
											<label
												htmlFor="file-upload"
												className="cursor-pointer flex flex-col items-center"
											>
												<Upload className="size-8 mb-2 text-muted-foreground" />
												<span className="text-sm font-medium">
													Drop files here or click to upload
												</span>
												<span className="text-xs text-muted-foreground mt-1">
													PDF, DOC, DOCX (Max 10MB each)
												</span>
											</label>
											{uploadedFiles.length > 0 && (
												<div className="mt-4">
													<p className="text-sm font-medium mb-2">
														Selected files:
													</p>
													<ul className="text-sm text-muted-foreground space-y-1">
														{uploadedFiles.map((file, index) => (
															<li
																key={index}
																className="flex items-center justify-between"
															>
																<div className="flex items-center gap-2">
																	<FileTextIcon className="size-4" />
																	{file.name}
																</div>
																<Button
																	type="button"
																	variant="ghost"
																	size="sm"
																	onClick={() => handleFileDelete(index)}
																	disabled={deletingFiles.includes(file.name)}
																	className="h-8 w-8 p-0"
																>
																	<X className="h-4 w-4" />
																	<span className="sr-only">Remove file</span>
																</Button>
															</li>
														))}
													</ul>
												</div>
											)}
										</div>

										<Button
											type="button"
											onClick={() => generateProfile(uploadedFiles)}
											disabled={
												isGeneratingProfile || uploadedFiles.length === 0
											}
											className="w-full gap-2"
										>
											{isGeneratingProfile ? (
												<Loader />
											) : (
												<FileTextIcon className="size-4" />
											)}
											{isGeneratingProfile
												? "Generating Profile..."
												: "Generate AI Profile"}
										</Button>

										<FormField
											control={form.control}
											name="aiGeneratedProfile"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-[rgb(var(--base-navy))]">
														Generated Profile
													</FormLabel>
													<FormControl>
														<Textarea
															{...field}
															className="bg-background resize-none min-h-[200px] sm:text-base text-sm focus-visible:ring-[rgb(var(--base-navy))]"
															placeholder="AI-generated profile will appear here"
														/>
													</FormControl>
													<FormDescription className="text-[rgb(var(--base-navy))]/70">
														You can edit this profile to better reflect your
														style
													</FormDescription>
												</FormItem>
											)}
										/>
									</CardContent>
								</Card>
							</motion.div>
						</TabsContent>
					</Tabs>
					<div className="flex items-center justify-end mb-8">
						<Button
							type="button" // Change from "submit" to "button"
							className="navy-button gap-2 mt-8 bg-[rgb(var(--base-navy))] hover:bg-[rgb(var(--base-navy))] text-white"
							disabled={isSubmitting || !isDirty}
							onClick={() => {
								// Get the current form values
								const formData = form.getValues();

								// Call onSubmit directly with the form data
								onSubmit(formData);
							}}
						>
							{isSubmitting ? (
								<>
									<Loader className="animate-spin" />
									Saving...
								</>
							) : (
								<>
									<Save className="size-4" />
									{isDirty ? "Save Changes" : "Saved"}
								</>
							)}
						</Button>
					</div>
				</form>
			</div>
		</Form>
	);
}
