"use client";

import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useTheme } from "next-themes";
import { HomeIcon, Loader2, Minimize2, MoveDiagonal } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { LogoIqidis } from "./icons";
import { PageLoader } from "@/components/ui/page-loader";

import Link from "next/link";
import { ThemeToggle } from "./theme-toggle";
import { UserMenu } from "./user-menu";

export function CommonHeader({ brandingOnly = false }) {
	const router = useRouter();
	const pathname = usePathname();
	const { resolvedTheme } = useTheme();
	const [showMenuButtons, setShowMenuButtons] = useState(true);
	const [isLoading, setIsLoading] = useState(false);

	const handleShowMenuState = (status: boolean) => {
		setShowMenuButtons(status);
	};

	// Intercept navigation to show loader
	const handleRoute = (path: string) => {
		if (pathname !== path) {
			// Set global loading state
			setIsLoading(true);

			// For subscription pages, set a flag in localStorage
			if (path.includes("/subscription") || path.includes("/welcome")) {
				localStorage.setItem("navigationInProgress", "true");
			}

			router.push(path);
		}
	};

	// Clear loading state when navigation completes
	useEffect(() => {
		// Only clear loading for non-subscription pages
		// For subscription pages, the SubscriptionPageContent will handle this
		if (!pathname.includes("/subscription") && !pathname.includes("/welcome")) {
			setIsLoading(false);
		}

		// Cleanup function
		return () => {
			if (
				!pathname.includes("/subscription") &&
				!pathname.includes("/welcome")
			) {
				localStorage.removeItem("navigationInProgress");
			}
		};
	}, [pathname]);

	return (
		<>
			<header
				className="flex py-4 items-center px-2 md:px-6 gap-2 z-[50] backdrop-blur-md backdrop-saturate-150 glassmorphic-header justify-between bg-white dark:bg-background"
				style={{
					//   backgroundColor:
					//     resolvedTheme === "dark"
					//       ? "var(--header-bg-dark)"
					//       : "rgba(255, 255, 255, 0.35)",
					//   backgroundImage:
					//     resolvedTheme === "dark"
					//       ? "linear-gradient(to bottom, var(--header-gradient-dark), var(--header-gradient-dark-end))"
					//       : "linear-gradient(to bottom, rgba(255, 255, 255, 0.65), rgba(255, 255, 255, 0.35))",
					boxShadow:
						resolvedTheme === "dark"
							? `0 1px 8px var(--header-shadow-dark), inset 0 1px 0 0 var(--header-highlight-dark)`
							: "0 1px 8px rgba(0, 0, 0, 0.05), inset 0 1px 0 0 rgba(255, 255, 255, 0.9)",
					borderBottom:
						resolvedTheme === "dark"
							? `1px solid rgb(63 63 70)`
							: "1px solid rgba(255, 255, 255, 0.5)",
					height: "72px",
				}}
			>
				<div className="flex items-center gap-2">
					<Link
						href="/"
						className="flex flex-row gap-3 items-center focus:outline-none"
					>
						<div className="flex items-center gap-2 px-1 py-1 rounded-md cursor-pointer focus-within:ring-2 focus-within:ring-[rgb(var(--base-navy))] focus-visible:ring-2 focus-visible:ring-[rgb(var(--base-navy))] focus:outline-none transition-all duration-200">
							<div className="relative w-10 h-10 flex items-center justify-center focus:outline-none">
								<LogoIqidis
									size={56}
									mixBlendMode={
										resolvedTheme === "dark" ? "lighten" : "multiply"
									}
									isDark={resolvedTheme === "dark"}
								/>
							</div>
						</div>
					</Link>

					{showMenuButtons ? (
						<div className="flex items-center gap-4 min-w-0 flex-1">
							<span
								className="text-xl transition-colors duration-200 cursor-pointer"
								onClick={() => router.push("/")}
							>
								<span className="font-bold mobileHide">Iqidis</span>
								<span className="font-normal mobileHide"> Core</span>
							</span>
							{/* <Button
                variant={"secondary"}
                className="hover:shadow-none"
                onClick={() => handleShowMenuState(false)}
              >
                <Minimize2 size={18} />
              </Button> */}

							{!brandingOnly && (
								<Button
									variant={"secondary"}
									className="hover:shadow-none"
									onClick={() => handleRoute?.("/")}
								>
									<span>
										<HomeIcon />
									</span>
									<span className="homeBtnTextHide">Home</span>
								</Button>
							)}
						</div>
					) : !brandingOnly ? (
						<Button
							variant={"secondary"}
							className="hover:shadow-none"
							onClick={() => handleShowMenuState(true)}
						>
							<MoveDiagonal size={18} />
						</Button>
					) : null}
				</div>

				{!brandingOnly && (
					<div className="flex items-center space-x-4 justify-end">
						<ThemeToggle className="hidden sm:flex" />
						<div className="relative z-[1000]">
							<UserMenu handleRoute={handleRoute} />
						</div>
					</div>
				)}
			</header>

			{/* Replace the custom loader with PageLoader */}
			{isLoading && <PageLoader message="Loading..." />}
		</>
	);
}
